# Steam Delisi - <PERSON><PERSON>

## 🎯 Proje Amacı
Steam oyunlarının manifest dosyalarını çıkarmak ve Lua scriptleri oluşturmak için modern bir masaüstü uygulaması geliştirmek.

## 👤 Müşteri Profili
- **İsmail Akçay** - apkdelisi.net
- Steam oyunları ile çalışan, manifest dosyalarına ihtiyaç duyan kullanıcı
- Modern UI/UX tasarım beklentisi olan
- Türkçe arayüz tercih eden

## 🎮 Temel İhtiyaçlar
1. Steam manifest dosyalarını otomatik bulma
2. En güncel sürüm manifest'lerini seçme
3. Lua script oluşturma
4. Modern, koyu tema arayüz
5. Kaynak kodunu gizleme (derleme)

## 📋 Temel Özellikler
- Steam depotcache otomatik bulma
- Oyun adlarını Steam API'den çekme
- DecryptionKey'leri Steam config'den okuma
- En güncel/tüm manifest seçim modu
- Dinamik klasör oluşturma
- Modern koyu tema UI

## 🚀 Sonuç
Tamamen çalışan, derlenmiş .exe uygulaması ve GitHub README dosyası hazır.