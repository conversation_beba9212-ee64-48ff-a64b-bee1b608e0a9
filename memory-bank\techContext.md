# Steam Delisi - Teknoloji Bağlamı

## 🐍 Ana Teknolojiler

### Python 3.x
- **Tkinter**: GUI framework (native, kurulum gerektirmez)
- **Requests**: Steam API çağrıları için
- **Re**: Regex pattern matching
- **Os/Shutil**: Dosya işlemleri
- **Json**: API response parsing

### <PERSON><PERSON>ş Bağımlılıklar
```python
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import requests  # Tek dış bağımlılık
```

## 🔧 Geliştirme Araçları

### Derleme: PyInstaller
```bash
pyinstaller --onefile --windowed --name "Steam-Lua-Exporter" steam-lua-exporter-modern.pyw
```

### Otomatik Derleme Scripts
- `compile.bat` - Akıllı PATH kontrolü
- `compile_alternative.bat` - Basit python -m yaklaşımı
- `manual_compile.txt` - <PERSON> komutlar

## 🎨 UI Framework: Tkinter

### Avantajları
- Python ile native gelir
- Kurulum gerektirmez
- Hafif ve hızlı
- Windows native görünüm

### Modern Styling Yaklaşımı
```python
# Koyu tema renk paleti
colors = {
    'bg_primary': '#2d2d30',
    'bg_secondary': '#3c3c3c',
    'accent': '#0078d4'
}

# Custom button styling
relief=tk.FLAT, bd=0, cursor='hand2'
```

## 🔌 Steam Entegrasyonu

### Steam API
- **Endpoint**: `https://store.steampowered.com/api/appdetails`
- **Amaç**: Oyun adlarını çekme
- **Timeout**: 10 saniye
- **Fallback**: API başarısız olursa ID kullan

### Steam VDF Parsing
```python
# config.vdf formatı
pattern = r'"(\d+)"\s*{\s*"DecryptionKey"\s*"([a-fA-F0-9]+)"'
```

### Steam Dizin Bulma
```python
possible_paths = [
    "C:\\Program Files (x86)\\Steam",
    "C:\\Program Files\\Steam", 
    os.path.expanduser("~\\Steam")
]
```

## 📁 Dosya Formatları

### Manifest Dosyaları
- **Format**: `{depot_id}_{manifest_id}.manifest`
- **Konum**: `Steam/depotcache/`
- **Binary**: İçerik okunmaz, sadece isim parse edilir

### VDF Dosyaları (Steam Config)
- **Konum**: `Steam/config/config.vdf`
- **Encoding**: UTF-8 with error ignore
- **Parse**: Regex ile DecryptionKey çekme

### Lua Script Çıktısı
```lua
addappid({app_id})
addappid(3420050)
addappid({depot_id},0,"{decryption_key}")
setManifestid({depot_id},"{manifest_id}")
```

## 🚀 Performans Optimizasyonları

### Dosya İşlemleri
- `shutil.copy2()` - Metadata korunarak kopyalama
- `os.makedirs(exist_ok=True)` - Güvenli klasör oluşturma
- Lazy loading - Sadece gerektiğinde API çağrısı

### UI Responsiveness
- `self.root.update()` - Log mesajları için
- Non-blocking operations
- Progress feedback ile kullanıcı bilgilendirme

## 🛡️ Güvenlik & Hata Yönetimi

### Exception Handling
```python
try:
    # Risky operation
except Exception as e:
    self.log_message(f"❌ Hata: {str(e)}", "error")
```

### Input Validation
- App ID numeric check
- Path existence validation
- File extension filtering

### Fallback Mechanisms
- Steam bulunamazsa manuel seçim
- API başarısız olursa ID kullanımı
- DecryptionKey bulunamazsa default hash

## 📦 Dağıtım

### PyInstaller Konfigürasyonu
- `--onefile`: Tek executable
- `--windowed`: Console window gizle
- `--name`: Custom executable adı
- `--distpath "."`: Current directory'ye çıktı

### Dosya Yapısı
```
steam-lua-exporter/
├── steam-lua-exporter-modern.pyw  # Ana kod
├── Steam-Lua-Exporter.exe         # Derlenmiş uygulama
├── compile.bat                     # Derleme script
├── README.md                       # Kullanım rehberi
└── memory-bank/                    # Proje belgeleri
```

## 🔄 Versiyon Yönetimi

### Dosya Adlandırma
- `.pyw` extension - Windows'ta console gizleme
- Modern suffix - Gelişmiş versiyon belirtme

### Backward Compatibility
- Eski manifest formatları desteklenir
- Steam path değişikliklerine uyum
- API değişikliklerine karşı fallback