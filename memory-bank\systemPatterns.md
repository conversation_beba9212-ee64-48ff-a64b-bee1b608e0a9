# Steam Delisi - Sistem Mimarisi

## 🏗️ Uygulama Mimarisi
**Tek Dosya Tkinter Uygulaması** - `steam-lua-exporter-modern.pyw`

### Ana Sınıf: `SteamLuaExporter`
- GUI yönetimi
- Steam entegrasyonu
- Dosya işlemleri
- Kullanıcı etkileşimi

## 🔧 Temel Bileşenler

### 1. Steam Entegrasyonu
```python
find_steam_path() -> Steam kurulum dizini bulma
get_decryption_keys() -> config.vdf'den DecryptionKey çekme
get_game_name() -> Steam API'den oyun adı alma
```

### 2. Manifest İşleme
```python
scan_manifests() -> Manifest dosyalarını tarama ve gruplama
- Depot ID'ye göre gruplama
- En güncel/tüm manifest seçimi
- Pattern: depotid_manifestid.manifest
```

### 3. Lua <PERSON>
```python
create_lua() -> Doğru formatta Lua script oluşturma
- addappid() komutları
- DecryptionKey entegrasyonu
- setManifestid() komutları
```

## 📁 Dosya Yapısı Patterns

### Manifest Dosya Formatı
`{depot_id}_{manifest_id}.manifest`
- Örnek: `2531311_5151442398504601055.manifest`

### Çıktı Klasör Yapısı
```
çıktı/
└── {app_id}_{game_name}/
    ├── {app_id}.lua
    └── *.manifest
```

### Lua Script Formatı
```lua
-- Header comments
addappid({app_id})
addappid(3420050)
addappid({depot_id},0,"{decryption_key}")
setManifestid({depot_id},"{manifest_id}")
```

## 🎨 UI Patterns

### Layout Yapısı
- **Header**: Başlık + Yazar bilgisi
- **Top Panel**: Input alanları + Butonlar
- **Bottom Panel**: Log çıktıları

### Renk Şeması (Koyu Tema)
```python
colors = {
    'bg_primary': '#2d2d30',
    'bg_secondary': '#3c3c3c', 
    'bg_input': '#1e1e1e',
    'accent': '#0078d4',
    'success': '#16c60c',
    'error': '#f14c4c',
    'warning': '#ffcc02'
}
```

## 🔄 İş Akışı Patterns

### 1. Başlatma Sırası
1. Steam path bulma
2. Depotcache path ayarlama
3. Çıktı klasörü ayarlama
4. UI başlatma

### 2. Manifest Tarama Sırası
1. App ID validasyonu
2. Manifest klasörü kontrolü
3. Dosya tarama (prefix matching)
4. Depot gruplama
5. Sürüm seçimi (min/max ID)

### 3. Lua Oluşturma Sırası
1. Manifest seçim kontrolü
2. Çıktı klasörü oluşturma
3. Manifest dosyalarını kopyalama
4. DecryptionKey çekme
5. Lua content oluşturma
6. Dosya yazma

## 🛡️ Hata Yönetimi Patterns
- Try-catch blokları her kritik işlemde
- Kullanıcı dostu hata mesajları
- Log sistemi ile detaylı takip
- Graceful fallback'ler (varsayılan hash'ler)

## 📦 Derleme Patterns
- PyInstaller ile tek dosya
- `--onefile --windowed` parametreleri
- Otomatik batch script'ler
- PATH sorunları için alternatif yöntemler