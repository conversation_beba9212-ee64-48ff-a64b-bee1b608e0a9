{"name": "steam-lua-exporter", "version": "1.0.0", "description": "Steam Delisi - Modern Electron Version", "main": "main.js", "scripts": {"start": "electron .", "build": "electron-builder", "build-portable": "electron-builder --win --x64 --config.win.target=portable", "dist": "electron-builder --publish=never"}, "author": "İsmail Akçay - apkdelisi.net", "license": "MIT", "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.13.3"}, "dependencies": {"firebase": "^10.7.1"}, "build": {"appId": "com.apkdelisi.steam-lua-exporter", "productName": "Steam Delisi", "directories": {"output": "dist"}, "files": ["**/*", "!dist/**/*", "!.git/**/*"], "asarUnpack": ["node_modules/firebase/**/*"], "win": {"target": [{"target": "nsis", "arch": ["x64"]}, {"target": "portable", "arch": ["x64"]}], "icon": "logo.png", "requestedExecutionLevel": "asInvoker"}, "portable": {"artifactName": "${productName}-${version}-portable.${ext}"}}}