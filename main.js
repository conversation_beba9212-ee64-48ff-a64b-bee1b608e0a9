const { app, <PERSON><PERSON>er<PERSON>indow, ipc<PERSON>ain, dialog } = require('electron');
const path = require('path');
const fs = require('fs').promises;
const fsSync = require('fs');
const https = require('https');
const os = require('os');

// Firebase Firestore
const {
  db,
  doc,
  setDoc,
  getDoc,
  updateDoc,
  deleteDoc,
  collection,
  getDocs,
  addDoc
} = require('./firebase-config');

let mainWindow;

// Utility function to check if file exists
async function fileExists(filePath) {
  try {
    await fs.access(filePath);
    return true;
  } catch {
    return false;
  }
}

// Utility function to ensure directory exists
async function ensureDir(dirPath) {
  try {
    await fs.mkdir(dirPath, { recursive: true });
  } catch (error) {
    if (error.code !== 'EEXIST') {
      throw error;
    }
  }
}

// Utility function for HTTP requests
function httpsGet(url) {
  return new Promise((resolve, reject) => {
    const request = https.get(url, { timeout: 10000 }, (response) => {
      let data = '';
      
      response.on('data', (chunk) => {
        data += chunk;
      });
      
      response.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({ data: jsonData });
        } catch (error) {
          reject(new Error('Invalid JSON response'));
        }
      });
    });
    
    request.on('error', (error) => {
      reject(error);
    });
    
    request.on('timeout', () => {
      request.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

// Function to get application settings from Firestore
async function getAppSettings() {
  try {
    const settingsDoc = await getDoc(doc(db, 'settings', 'app_config'));
    if (settingsDoc.exists() && settingsDoc.data().downloadUrlTemplate) {
      console.log('DEBUG: Custom download URL found in Firestore.');
      return settingsDoc.data();
    }
    // Return default settings if not found in Firestore or field is missing
    console.log('DEBUG: No custom download URL in Firestore, using default.');
    return {
      downloadUrlTemplate: 'https://steamdatabase1.s3.eu-north-1.amazonaws.com/{appId}.zip'
    };
  } catch (error) {
    console.error('Error fetching app settings from Firestore:', error);
    // Fallback to default on error
    return {
      downloadUrlTemplate: 'https://steamdatabase1.s3.eu-north-1.amazonaws.com/{appId}.zip'
    };
  }
}

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 800,
    minWidth: 1200,
    minHeight: 700,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true
    },
    icon: path.join(__dirname, 'logo.png'),
    titleBarStyle: 'default',
    autoHideMenuBar: true,
    show: false
  });

  // Remove menu bar completely
  mainWindow.setMenuBarVisibility(false);

  mainWindow.loadFile('login.html');
  
  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // Open DevTools in development
  // mainWindow.webContents.openDevTools();
}

// Function to copy hid.dll to Steam directory if it doesn't exist
async function copyHidDllToSteam() {
  try {
    const steamPath = findSteamPath();
    if (!steamPath) {
      console.log('DEBUG: Steam path not found, skipping hid.dll copy');
      return;
    }
    
    const sourceDll = path.join(__dirname, 'hid.dll');
    const targetDll = path.join(steamPath, 'hid.dll');
    
    // Check if source dll exists
    if (!await fileExists(sourceDll)) {
      console.log('DEBUG: hid.dll not found in application directory');
      return;
    }
    
    // Check if target dll already exists
    if (await fileExists(targetDll)) {
      console.log('DEBUG: hid.dll already exists in Steam directory, skipping copy');
      return;
    }
    
    // Copy the dll file
    await fs.copyFile(sourceDll, targetDll);
    console.log(`DEBUG: Successfully copied hid.dll to Steam directory: ${targetDll}`);
    
  } catch (error) {
    console.error('DEBUG: Error copying hid.dll to Steam directory:', error);
  }
}

app.whenReady().then(async () => {
  await copyHidDllToSteam();
  createWindow();
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// Steam path finder
function findSteamPath() {
  // Windows'ta Steam yolunu bulmak için öncelik sırası:
  // 1. Kayıt Defteri (En güvenilir yöntem)
  // 2. Varsayılan yollar (Geriye dönük uyumluluk)

  if (process.platform === 'win32') {
    try {
      const { execSync } = require('child_process');
      const command = 'reg query "HKCU\\Software\\Valve\\Steam" /v SteamPath';
      const stdout = execSync(command).toString();
      
      // Çıktıdan yolu ayıkla
      const match = stdout.match(/SteamPath\s+REG_SZ\s+(.+)/);
      if (match && match[1]) {
        const steamPath = match[1].trim();
        if (fsSync.existsSync(steamPath)) {
          console.log(`DEBUG: Steam path found in registry: ${steamPath}`);
          return steamPath;
        }
      }
    } catch (error) {
      console.log('DEBUG: Could not find Steam path in registry, checking default locations...');
    }
  }

  // Kayıt defteri başarısız olursa veya platform Windows değilse, varsayılan yolları dene
  const possiblePaths = [
    'C:\\Program Files (x86)\\Steam',
    'C:\\Program Files\\Steam',
    path.join(os.homedir(), 'Steam')
    // Diğer işletim sistemleri için yollar buraya eklenebilir
  ];

  for (const steamPath of possiblePaths) {
    if (fsSync.existsSync(steamPath)) {
      console.log(`DEBUG: Steam path found in default location: ${steamPath}`);
      return steamPath;
    }
  }

  console.log('DEBUG: Steam path could not be found in any location.');
  return null;
}

// Helper function for getting decryption keys
async function getDecryptionKeys() {
  const steamPath = findSteamPath();
  if (!steamPath) return {};
  
  const configPath = path.join(steamPath, 'config', 'config.vdf');
  if (!fsSync.existsSync(configPath)) return {};
  
  try {
    const content = await fs.readFile(configPath, 'utf-8');
    const pattern = /"(\d+)"\s*{\s*"DecryptionKey"\s*"([a-fA-F0-9]+)"/g;
    const decryptionKeys = {};
    
    let match;
    while ((match = pattern.exec(content)) !== null) {
      decryptionKeys[match[1]] = match[2];
    }
    
    return decryptionKeys;
  } catch (error) {
    console.error('Error reading decryption keys:', error);
    return {};
  }
}

// IPC Handlers
ipcMain.handle('find-steam-path', async () => {
  return findSteamPath();
});

ipcMain.handle('get-desktop-path', async () => {
  return path.join(os.homedir(), 'Desktop');
});

ipcMain.handle('get-game-name', async (event, appId) => {
  try {
    const response = await httpsGet(`https://store.steampowered.com/api/appdetails?appids=${appId}&l=turkish`, {
      timeout: 10000
    });
    
    if (response.data[appId] && response.data[appId].success) {
      return response.data[appId].data.name;
    }
  } catch (error) {
    console.error('Error fetching game name:', error);
  }
  return null;
});

ipcMain.handle('get-game-image', async (event, appId) => {
  // Steam'in standart kapak görseli URL formatı
  return `https://cdn.akamai.steamstatic.com/steam/apps/${appId}/header.jpg`;
});

ipcMain.handle('search-games', async (event, query) => {
  try {
    if (!query || query.trim().length < 2) {
      return { success: true, games: [] };
    }
    
    // Steam Store Search API kullanarak oyun arama
    const searchUrl = `https://store.steampowered.com/api/storesearch/?term=${encodeURIComponent(query)}&l=turkish&cc=TR`;
    
    console.log(`DEBUG: Searching games with query: ${query}`);
    console.log(`DEBUG: Search URL: ${searchUrl}`);
    
    const response = await httpsGet(searchUrl);
    
    if (response.data && response.data.items) {
      // Sadece oyunları filtrele (type: 'app')
      const games = response.data.items
        .filter(item => item.type === 'app')
        .slice(0, 10) // İlk 10 sonucu al
        .map(item => ({
          id: item.id.toString(),
          name: item.name,
          image: `https://cdn.akamai.steamstatic.com/steam/apps/${item.id}/capsule_sm_120.jpg`
        }));
      
      console.log(`DEBUG: Found ${games.length} games for query: ${query}`);
      return { success: true, games };
    }
    
    return { success: true, games: [] };
  } catch (error) {
    console.error('Error searching games:', error);
    return { success: false, error: error.message, games: [] };
  }
});

ipcMain.handle('restart-steam', async () => {
  try {
    const { exec } = require('child_process');
    const util = require('util');
    const execAsync = util.promisify(exec);
    
    // Steam'i kapat
    try {
      await execAsync('taskkill /f /im steam.exe');
      console.log('Steam kapatıldı');
    } catch (error) {
      console.log('Steam zaten kapalı veya kapatılamadı');
    }
    
    // 2 saniye bekle
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Steam'i yeniden başlat
    const steamPath = findSteamPath();
    if (steamPath) {
      const steamExe = path.join(steamPath, 'steam.exe');
      exec(`"${steamExe}"`, (error) => {
        if (error) {
          console.error('Steam başlatılamadı:', error);
        } else {
          console.log('Steam başlatıldı');
        }
      });
      return { success: true };
    } else {
      throw new Error('Steam yolu bulunamadı!');
    }
    
  } catch (error) {
    console.error('Steam yeniden başlatma hatası:', error);
    throw error;
  }
});

ipcMain.handle('get-installed-games', async () => {
  const steamPath = findSteamPath();
  if (!steamPath) return [];
  
  const stplugPath = path.join(steamPath, 'config', 'stplug-in');
  if (!fsSync.existsSync(stplugPath)) return [];
  
  try {
    const files = await fs.readdir(stplugPath);
    const luaFiles = files
      .filter(file => file.endsWith('.lua') && /^\d+\.lua$/.test(file))
      .map(file => file.replace('.lua', ''));
    
    return luaFiles;
  } catch (error) {
    console.error('Error reading installed games:', error);
    return [];
  }
});

ipcMain.handle('get-manifest-count', async (event, appId) => {
  const steamPath = findSteamPath();
  if (!steamPath) return 0;
  
  // Check config depotcache first (where imported files are stored)
  const configDepotcachePath = path.join(steamPath, 'config', 'depotcache');
  const mainDepotcachePath = path.join(steamPath, 'depotcache');
  
  try {
    const appIdNum = parseInt(appId);
    let manifestCount = 0;
    
    // Check config depotcache first (priority for imported games)
    if (fsSync.existsSync(configDepotcachePath)) {
      const configFiles = await fs.readdir(configDepotcachePath);
      const configManifests = configFiles.filter(file => {
        if (!file.endsWith('.manifest')) return false;
        
        const depotMatch = file.match(/^(\d+)_/);
        if (depotMatch) {
          const depotId = parseInt(depotMatch[1]);
          const diff = Math.abs(depotId - appIdNum);
          return diff <= 10; // Same logic as scan-manifests
        }
        return false;
      });
      manifestCount += configManifests.length;
      console.log(`DEBUG: Found ${configManifests.length} manifests in config depotcache for app ${appId}`);
    }
    
    // If no manifests found in config, check main depotcache
    if (manifestCount === 0 && fsSync.existsSync(mainDepotcachePath)) {
      const mainFiles = await fs.readdir(mainDepotcachePath);
      const mainManifests = mainFiles.filter(file => {
        if (!file.endsWith('.manifest')) return false;
        
        const depotMatch = file.match(/^(\d+)_/);
        if (depotMatch) {
          const depotId = parseInt(depotMatch[1]);
          const diff = Math.abs(depotId - appIdNum);
          return diff <= 10; // Same logic as scan-manifests
        }
        return false;
      });
      manifestCount += mainManifests.length;
      console.log(`DEBUG: Found ${mainManifests.length} manifests in main depotcache for app ${appId}`);
    }
    
    return manifestCount;
  } catch (error) {
    console.error('Error counting manifests:', error);
    return 0;
  }
});

ipcMain.handle('scan-manifests', async (event, appId, mode, customDepotcachePath) => {
  // Use custom path if provided, otherwise use default Steam path
  let depotcachePath;
  
  if (customDepotcachePath && customDepotcachePath.trim()) {
    depotcachePath = customDepotcachePath.trim();
  } else {
    const steamPath = findSteamPath();
    if (!steamPath) throw new Error('Steam yolu bulunamadı!');
    depotcachePath = path.join(steamPath, 'depotcache');
  }
  
  if (!fsSync.existsSync(depotcachePath)) throw new Error(`Depotcache klasörü bulunamadı: ${depotcachePath}`);
  
  try {
    const files = await fs.readdir(depotcachePath);
    const appIdNum = parseInt(appId);
    let manifestFiles = [];
    
    // Debug: Show all manifest files
    const allManifests = files.filter(f => f.endsWith('.manifest'));
    console.log(`DEBUG: Total manifest files in depotcache: ${allManifests.length}`);
    console.log(`DEBUG: Looking for app ID: ${appId} (${appIdNum})`);
    
    // Show first 10 manifest files for debugging
    console.log(`DEBUG: First 10 manifest files:`, allManifests.slice(0, 10));
    
    // Strategy 1: Look for depot IDs close to app ID (within 50 numbers for debugging)
    manifestFiles = files.filter(file => {
      if (!file.endsWith('.manifest')) return false;
      
      const depotMatch = file.match(/^(\d+)_/);
      if (depotMatch) {
        const depotId = parseInt(depotMatch[1]);
        const diff = Math.abs(depotId - appIdNum);
        
        // Debug log for close matches
        if (diff <= 50) {
          console.log(`DEBUG: Close match found - Depot: ${depotId}, App: ${appIdNum}, Diff: ${diff}, File: ${file}`);
        }
        
        // Check if depot ID is close to app ID
        return diff <= 10;
      }
      return false;
    });
    
    console.log(`DEBUG: Strategy 1 found ${manifestFiles.length} files`);
    
    // Strategy 2: If no close match, try exact prefix (fallback)
    if (manifestFiles.length === 0) {
      const appPrefix = appId.substring(0, 4);
      manifestFiles = files.filter(file =>
        file.endsWith('.manifest') && file.startsWith(appPrefix)
      );
      console.log(`DEBUG: Strategy 2 (prefix ${appPrefix}) found ${manifestFiles.length} files`);
    }
    
    if (manifestFiles.length === 0) {
      throw new Error(`App ID ${appId} için manifest dosyası bulunamadı! Depotcache'de ${allManifests.length} manifest dosyası var ama hiçbiri eşleşmedi.`);
    }
    
    console.log(`Found ${manifestFiles.length} manifest files for app ${appId}:`, manifestFiles);
    
    // Group by depot and select based on mode
    const depotManifests = {};
    manifestFiles.forEach(filename => {
      const parts = filename.replace('.manifest', '').split('_');
      if (parts.length >= 2) {
        const depotId = parts[0];
        const manifestId = parts[1];
        
        if (!depotManifests[depotId]) {
          depotManifests[depotId] = [];
        }
        depotManifests[depotId].push({ filename, manifestId });
      }
    });
    
    let selectedManifests = [];
    
    for (const [depotId, manifests] of Object.entries(depotManifests)) {
      if (mode === 'all') {
        selectedManifests.push(...manifests.map(m => m.filename));
      } else {
        // Select newest (smallest manifest ID)
        const newest = manifests.reduce((min, current) => {
          const minId = parseInt(min.manifestId) || 0;
          const currentId = parseInt(current.manifestId) || 0;
          return currentId < minId ? current : min;
        });
        selectedManifests.push(newest.filename);
      }
    }
    
    return selectedManifests;
  } catch (error) {
    throw error;
  }
});

ipcMain.handle('count-manifests', async (event, folderPath) => {
  try {
    if (!folderPath || !folderPath.trim()) {
      return 0;
    }
    
    const manifestPath = folderPath.trim();
    if (!fsSync.existsSync(manifestPath)) {
      return 0;
    }
    
    const files = await fs.readdir(manifestPath);
    const manifestFiles = files.filter(file => file.endsWith('.manifest'));
    
    return manifestFiles.length;
  } catch (error) {
    console.error('Error counting manifests:', error);
    return 0;
  }
});

ipcMain.handle('check-config-file', async (event, filePath) => {
  try {
    if (!filePath || !filePath.trim()) {
      return false;
    }
    
    const configPath = filePath.trim();
    return fsSync.existsSync(configPath);
  } catch (error) {
    console.error('Error checking config file:', error);
    return false;
  }
});

ipcMain.handle('get-decryption-keys', async () => {
  const steamPath = findSteamPath();
  if (!steamPath) return {};
  
  const configPath = path.join(steamPath, 'config', 'config.vdf');
  if (!fsSync.existsSync(configPath)) return {};
  
  try {
    const content = await fs.readFile(configPath, 'utf-8');
    const pattern = /"(\d+)"\s*{\s*"DecryptionKey"\s*"([a-fA-F0-9]+)"/g;
    const decryptionKeys = {};
    
    let match;
    while ((match = pattern.exec(content)) !== null) {
      decryptionKeys[match[1]] = match[2];
    }
    
    return decryptionKeys;
  } catch (error) {
    console.error('Error reading decryption keys:', error);
    return {};
  }
});

ipcMain.handle('create-lua', async (event, appId, gameName, selectedManifests, outputPath) => {
  try {
    // Create output directory
    const safeName = gameName ? gameName.replace(/[<>:"/\\|?*]/g, '_') : '';
    const finalOutput = safeName ?
      path.join(outputPath, `${appId}_${safeName}`) :
      path.join(outputPath, appId);
    
    await ensureDir(finalOutput);
    console.log(`DEBUG: Created output directory: ${finalOutput}`);
    
    // Copy manifest files
    const steamPath = findSteamPath();
    const depotcachePath = path.join(steamPath, 'depotcache');
    console.log(`DEBUG: Source depotcache path: ${depotcachePath}`);
    console.log(`DEBUG: Copying ${selectedManifests.length} manifest files`);
    
    for (const manifestFile of selectedManifests) {
      const src = path.join(depotcachePath, manifestFile);
      const dst = path.join(finalOutput, manifestFile);
      
      console.log(`DEBUG: Copying manifest: ${src} -> ${dst}`);
      
      // Check if source exists
      if (!await fileExists(src)) {
        console.error(`DEBUG: Source manifest file not found: ${src}`);
        throw new Error(`Manifest dosyası bulunamadı: ${manifestFile}`);
      }
      
      await fs.copyFile(src, dst);
      
      // Verify copy was successful
      if (await fileExists(dst)) {
        console.log(`DEBUG: Successfully copied: ${manifestFile}`);
      } else {
        console.error(`DEBUG: Failed to copy: ${manifestFile}`);
        throw new Error(`Manifest dosyası kopyalanamadı: ${manifestFile}`);
      }
    }
    
    // Create Lua script
    const decryptionKeys = await getDecryptionKeys();
    console.log(`DEBUG: Found ${Object.keys(decryptionKeys).length} decryption keys`);
    
    let luaContent = `-- Steam Delisi
-- Made by İsmail Akçay - apkdelisi.net
-- App ID: ${appId}
${gameName ? `-- Oyun: ${gameName}` : ''}

addappid(${appId})
addappid(3420050)
`;
    
    // Add depot IDs with DecryptionKeys
    for (const manifestFile of selectedManifests) {
      const parts = manifestFile.replace('.manifest', '').split('_');
      if (parts.length >= 2) {
        const depotId = parts[0];
        const decryptionKey = decryptionKeys[depotId] || 'a'.repeat(64);
        luaContent += `addappid(${depotId},0,"${decryptionKey}")\n`;
        console.log(`DEBUG: Added depot ${depotId} with key: ${decryptionKey.substring(0, 8)}...`);
      }
    }
    
    // Add setManifestid lines
    for (const manifestFile of selectedManifests) {
      const parts = manifestFile.replace('.manifest', '').split('_');
      if (parts.length >= 2) {
        const depotId = parts[0];
        const manifestId = parts[1];
        luaContent += `setManifestid(${depotId},"${manifestId}")\n`;
        console.log(`DEBUG: Set manifest ${manifestId} for depot ${depotId}`);
      }
    }
    
    const luaFile = path.join(finalOutput, `${appId}.lua`);
    await fs.writeFile(luaFile, luaContent, 'utf-8');
    console.log(`DEBUG: Created Lua file: ${luaFile}`);
    
    return { success: true, outputPath: finalOutput };
  } catch (error) {
    console.error('DEBUG: Create-lua error:', error);
    throw error;
  }
});

// Steam'e import edilen manifest dosyalarını kontrol et
ipcMain.handle('verify-imported-manifests', async (event, manifestFiles) => {
  try {
    const steamPath = findSteamPath();
    if (!steamPath) throw new Error('Steam yolu bulunamadı!');
    
    const mainDepotcachePath = path.join(steamPath, 'depotcache');
    const configDepotcachePath = path.join(steamPath, 'config', 'depotcache');
    const verificationResults = [];
    
    for (const manifestFile of manifestFiles) {
      const mainManifestPath = path.join(mainDepotcachePath, manifestFile);
      const configManifestPath = path.join(configDepotcachePath, manifestFile);
      
      const mainExists = await fileExists(mainManifestPath);
      const configExists = await fileExists(configManifestPath);
      
      let result = {
        file: manifestFile,
        mainDepotcache: {
          exists: mainExists,
          size: 0,
          path: mainManifestPath
        },
        configDepotcache: {
          exists: configExists,
          size: 0,
          path: configManifestPath
        }
      };
      
      if (mainExists) {
        const stats = await fs.stat(mainManifestPath);
        result.mainDepotcache.size = stats.size;
        console.log(`DEBUG: Verified manifest in main depotcache: ${manifestFile} (${stats.size} bytes)`);
      } else {
        console.log(`DEBUG: Missing manifest in main depotcache: ${manifestFile}`);
      }
      
      if (configExists) {
        const stats = await fs.stat(configManifestPath);
        result.configDepotcache.size = stats.size;
        console.log(`DEBUG: Verified manifest in config depotcache: ${manifestFile} (${stats.size} bytes)`);
      } else {
        console.log(`DEBUG: Missing manifest in config depotcache: ${manifestFile}`);
      }
      
      verificationResults.push(result);
    }
    
    return verificationResults;
  } catch (error) {
    console.error('DEBUG: Verification error:', error);
    throw error;
  }
});

ipcMain.handle('delete-game', async (event, appId) => {
  try {
    const steamPath = findSteamPath();
    if (!steamPath) throw new Error('Steam yolu bulunamadı!');
    
    let deletedFiles = 0;
    
    // Delete lua file
    const stplugPath = path.join(steamPath, 'config', 'stplug-in');
    const luaFile = path.join(stplugPath, `${appId}.lua`);
    if (await fileExists(luaFile)) {
      await fs.unlink(luaFile);
      deletedFiles++;
      console.log(`DEBUG: Deleted Lua file: ${luaFile}`);
    }
    
    // Delete manifest files from both locations
    const mainDepotcachePath = path.join(steamPath, 'depotcache');
    const configDepotcachePath = path.join(steamPath, 'config', 'depotcache');
    const appIdNum = parseInt(appId);
    
    // Delete from main depotcache
    if (await fileExists(mainDepotcachePath)) {
      const files = await fs.readdir(mainDepotcachePath);
      
      for (const file of files) {
        if (!file.endsWith('.manifest')) continue;
        
        const depotMatch = file.match(/^(\d+)_/);
        if (depotMatch) {
          const depotId = parseInt(depotMatch[1]);
          const diff = Math.abs(depotId - appIdNum);
          
          // Delete if depot ID is close to app ID (same logic as scanning)
          if (diff <= 10) {
            await fs.unlink(path.join(mainDepotcachePath, file));
            deletedFiles++;
            console.log(`DEBUG: Deleted manifest from main depotcache: ${file} (depot ${depotId} close to app ${appIdNum})`);
          }
        }
      }
    }
    
    // Delete from config depotcache
    if (await fileExists(configDepotcachePath)) {
      const files = await fs.readdir(configDepotcachePath);
      
      for (const file of files) {
        if (!file.endsWith('.manifest')) continue;
        
        const depotMatch = file.match(/^(\d+)_/);
        if (depotMatch) {
          const depotId = parseInt(depotMatch[1]);
          const diff = Math.abs(depotId - appIdNum);
          
          // Delete if depot ID is close to app ID (same logic as scanning)
          if (diff <= 10) {
            await fs.unlink(path.join(configDepotcachePath, file));
            deletedFiles++;
            console.log(`DEBUG: Deleted manifest from config depotcache: ${file} (depot ${depotId} close to app ${appIdNum})`);
          }
        }
      }
    }
    
    console.log(`DEBUG: Total files deleted for app ${appId}: ${deletedFiles}`);
    return { success: true, deletedFiles };
  } catch (error) {
    console.error('DEBUG: Delete game error:', error);
    throw error;
  }
});

ipcMain.handle('export-game', async (event, appId, gameName) => {
  try {
    const steamPath = findSteamPath();
    if (!steamPath) throw new Error('Steam yolu bulunamadı!');
    
    // Use desktop as output folder for better compatibility
    const desktopPath = path.join(os.homedir(), 'Desktop');
    const outputFolder = path.join(desktopPath, 'Steam Delisi Exports');
    
    const safeName = gameName ? gameName.replace(/[<>:"/\\|?*]/g, '_') : '';
    const folderName = safeName ? `${appId}_${safeName}` : appId;
    const exportPath = path.join(outputFolder, folderName);
    
    console.log(`DEBUG: Export path: ${exportPath}`);
    await ensureDir(exportPath);
    
    let copiedFiles = 0;
    const appIdNum = parseInt(appId);
    
    // Copy lua file from config/stplug-in
    const stplugPath = path.join(steamPath, 'config', 'stplug-in');
    const luaFile = path.join(stplugPath, `${appId}.lua`);
    if (await fileExists(luaFile)) {
      await fs.copyFile(luaFile, path.join(exportPath, `${appId}.lua`));
      copiedFiles++;
      console.log(`DEBUG: Copied Lua file: ${appId}.lua`);
    }
    
    // Copy manifest files from config/depotcache (where imported files are stored)
    const configDepotcachePath = path.join(steamPath, 'config', 'depotcache');
    const mainDepotcachePath = path.join(steamPath, 'depotcache');
    
    let manifestFiles = [];
    let sourceDepotcachePath = null;
    
    // First, try to find manifests in config depotcache (priority for imported games)
    if (await fileExists(configDepotcachePath)) {
      const configFiles = await fs.readdir(configDepotcachePath);
      manifestFiles = configFiles.filter(file => {
        if (!file.endsWith('.manifest')) return false;
        
        const depotMatch = file.match(/^(\d+)_/);
        if (depotMatch) {
          const depotId = parseInt(depotMatch[1]);
          const diff = Math.abs(depotId - appIdNum);
          return diff <= 10; // Same logic as scan-manifests
        }
        return false;
      });
      
      if (manifestFiles.length > 0) {
        sourceDepotcachePath = configDepotcachePath;
        console.log(`DEBUG: Found ${manifestFiles.length} manifest files in config depotcache for export`);
      }
    }
    
    // If no manifests found in config depotcache, try main depotcache
    if (manifestFiles.length === 0 && await fileExists(mainDepotcachePath)) {
      const mainFiles = await fs.readdir(mainDepotcachePath);
      manifestFiles = mainFiles.filter(file => {
        if (!file.endsWith('.manifest')) return false;
        
        const depotMatch = file.match(/^(\d+)_/);
        if (depotMatch) {
          const depotId = parseInt(depotMatch[1]);
          const diff = Math.abs(depotId - appIdNum);
          return diff <= 10; // Same logic as scan-manifests
        }
        return false;
      });
      
      if (manifestFiles.length > 0) {
        sourceDepotcachePath = mainDepotcachePath;
        console.log(`DEBUG: Found ${manifestFiles.length} manifest files in main depotcache for export`);
      }
    }
    
    // Copy manifest files from the source location
    if (sourceDepotcachePath && manifestFiles.length > 0) {
      console.log(`DEBUG: Copying manifests from: ${sourceDepotcachePath}`);
      
      for (const file of manifestFiles) {
        const src = path.join(sourceDepotcachePath, file);
        const dst = path.join(exportPath, file);
        await fs.copyFile(src, dst);
        copiedFiles++;
        console.log(`DEBUG: Copied manifest file: ${file}`);
      }
    } else {
      console.log(`DEBUG: No manifest files found for app ${appId} in either location`);
    }
    
    console.log(`DEBUG: Export completed. Total files copied: ${copiedFiles}`);
    return { success: true, copiedFiles, exportPath };
  } catch (error) {
    console.error('DEBUG: Export game error:', error);
    throw error;
  }
});

ipcMain.handle('select-folder', async () => {
  const result = await dialog.showOpenDialog(mainWindow, {
    properties: ['openDirectory']
  });
  
  return result.canceled ? null : result.filePaths[0];
});

ipcMain.handle('select-steam-path', async () => {
  const result = await dialog.showOpenDialog(mainWindow, {
    properties: ['openDirectory'],
    title: 'Steam Kurulum Klasörünü Seçin'
  });
  return result.canceled ? null : result.filePaths[0];
});

ipcMain.handle('select-config-file', async () => {
  const result = await dialog.showOpenDialog(mainWindow, {
    properties: ['openFile'],
    filters: [
      { name: 'VDF Files', extensions: ['vdf'] },
      { name: 'All Files', extensions: ['*'] }
    ]
  });
  
  return result.canceled ? null : result.filePaths[0];
});

ipcMain.handle('open-folder', async (event, folderPath) => {
  const { shell } = require('electron');
  shell.openPath(folderPath);
});

ipcMain.handle('import-files-to-steam', async (event, files) => {
  try {
    const steamPath = findSteamPath();
    if (!steamPath) throw new Error('Steam yolu bulunamadı!');
    
    const depotcachePath = path.join(steamPath, 'depotcache');
    const stplugPath = path.join(steamPath, 'config', 'stplug-in');
    
    // Ensure directories exist
    await ensureDir(depotcachePath);
    await ensureDir(stplugPath);
    
    console.log(`DEBUG: Depotcache path: ${depotcachePath}`);
    console.log(`DEBUG: Stplug-in path: ${stplugPath}`);
    console.log(`DEBUG: Directories created/verified`);
    
    let copiedFiles = 0;
    let processedGames = new Set();
    let manifestFiles = [];
    let luaFiles = [];
    
    for (const file of files) {
      const fileName = path.basename(file.path);
      const fileExt = path.extname(fileName).toLowerCase();
      
      if (fileExt === '.lua') {
        // Copy lua file to stplug-in
        const dst = path.join(stplugPath, fileName);
        console.log(`DEBUG: Copying Lua file: ${file.path} -> ${dst}`);
        await fs.copyFile(file.path, dst);
        copiedFiles++;
        luaFiles.push(fileName);
        
        // Extract app ID from filename
        const appId = fileName.replace('.lua', '');
        if (/^\d+$/.test(appId)) {
          processedGames.add(appId);
        }
        
      } else if (fileExt === '.manifest') {
        // Copy manifest file to both locations
        const mainDepotcacheDst = path.join(depotcachePath, fileName);
        const configDepotcachePath = path.join(steamPath, 'config', 'depotcache');
        const configDepotcacheDst = path.join(configDepotcachePath, fileName);
        
        console.log(`DEBUG: Copying manifest file: ${file.path}`);
        console.log(`DEBUG: -> Main depotcache: ${mainDepotcacheDst}`);
        console.log(`DEBUG: -> Config depotcache: ${configDepotcacheDst}`);
        
        // Check if source file exists and is readable
        if (!await fileExists(file.path)) {
          console.error(`DEBUG: Source manifest file does not exist: ${file.path}`);
          continue;
        }
        
        // Ensure config depotcache directory exists
        await ensureDir(configDepotcachePath);
        console.log(`DEBUG: Config depotcache directory ensured: ${configDepotcachePath}`);
        
        // Copy to main depotcache
        await fs.copyFile(file.path, mainDepotcacheDst);
        copiedFiles++;
        
        // Copy to config depotcache
        await fs.copyFile(file.path, configDepotcacheDst);
        copiedFiles++;
        
        manifestFiles.push(fileName);
        
        // Verify both files were copied
        if (await fileExists(mainDepotcacheDst)) {
          console.log(`DEBUG: Manifest file successfully copied to main depotcache: ${mainDepotcacheDst}`);
        } else {
          console.error(`DEBUG: Failed to copy manifest file to main depotcache: ${mainDepotcacheDst}`);
        }
        
        if (await fileExists(configDepotcacheDst)) {
          console.log(`DEBUG: Manifest file successfully copied to config depotcache: ${configDepotcacheDst}`);
        } else {
          console.error(`DEBUG: Failed to copy manifest file to config depotcache: ${configDepotcacheDst}`);
        }
      }
    }
    
    // Verify final state
    console.log(`DEBUG: Total files copied: ${copiedFiles}`);
    console.log(`DEBUG: Lua files copied: ${luaFiles.length} - ${luaFiles.join(', ')}`);
    console.log(`DEBUG: Manifest files copied: ${manifestFiles.length} - ${manifestFiles.join(', ')}`);
    
    // List actual files in depotcache to verify
    try {
      const depotFiles = await fs.readdir(depotcachePath);
      const actualManifests = depotFiles.filter(f => f.endsWith('.manifest'));
      console.log(`DEBUG: Actual manifest files in depotcache: ${actualManifests.length}`);
      console.log(`DEBUG: Recently copied manifests found: ${manifestFiles.filter(f => actualManifests.includes(f)).length}`);
    } catch (error) {
      console.error('DEBUG: Error reading depotcache directory:', error);
    }
    
    return {
      success: true,
      copiedFiles,
      processedGames: Array.from(processedGames),
      depotcachePath,
      stplugPath,
      manifestFiles,
      luaFiles
    };
    
  } catch (error) {
    console.error('DEBUG: Import error:', error);
    throw error;
  }
});

ipcMain.handle('select-import-files', async () => {
  const result = await dialog.showOpenDialog(mainWindow, {
    properties: ['openFile', 'multiSelections'],
    filters: [
      { name: 'Steam Files', extensions: ['lua', 'manifest'] },
      { name: 'Lua Files', extensions: ['lua'] },
      { name: 'Manifest Files', extensions: ['manifest'] },
      { name: 'All Files', extensions: ['*'] }
    ]
  });
  
  if (result.canceled) return null;
  
  const files = [];
  for (const filePath of result.filePaths) {
    const stats = await fs.stat(filePath);
    files.push({
      path: filePath,
      name: path.basename(filePath),
      size: stats.size,
      type: path.extname(filePath).toLowerCase().slice(1)
    });
  }
  
  return files;
});

// Analyze files to get game information
ipcMain.handle('analyze-import-files', async (event, files) => {
  const gameInfo = [];
  const manifestCounts = {};
  
  // First, count manifest files by their depot IDs (not app IDs)
  for (const file of files) {
    if (file.type === 'manifest') {
      // Just count manifests, don't try to extract app ID from them
      const depotMatch = file.name.match(/^(\d+)_\d+\.manifest$/);
      if (depotMatch) {
        const depotId = depotMatch[1];
        manifestCounts[depotId] = (manifestCounts[depotId] || 0) + 1;
      }
    }
  }
  
  // Then, process only Lua files to get actual game information
  for (const file of files) {
    try {
      if (file.type === 'lua') {
        // Extract app ID from lua filename (e.g., "123456.lua")
        const appIdMatch = file.name.match(/^(\d+)\.lua$/);
        if (appIdMatch) {
          const appId = appIdMatch[1];
          const gameName = await getGameName(appId);
          
          // Count total manifests for this game (estimate based on app prefix)
          const appPrefix = appId.substring(0, 4);
          let totalManifests = 0;
          
          Object.keys(manifestCounts).forEach(depotId => {
            if (depotId.startsWith(appPrefix)) {
              totalManifests += manifestCounts[depotId];
            }
          });
          
          gameInfo.push({
            appId,
            gameName: gameName || `Bilinmeyen Oyun (ID: ${appId})`,
            fileName: file.name,
            fileType: 'lua',
            manifestCount: totalManifests
          });
        }
      }
    } catch (error) {
      console.error('Error analyzing file:', file.name, error);
    }
  }
  
  return gameInfo;
});

// Helper function to get game name (reuse existing function)
async function getGameName(appId) {
  try {
    const response = await httpsGet(`https://store.steampowered.com/api/appdetails?appids=${appId}&l=turkish`, {
      timeout: 10000
    });
    
    if (response.data[appId] && response.data[appId].success) {
      return response.data[appId].data.name;
    }
  } catch (error) {
    console.error('Error fetching game name:', error);
  }
  return null;
}

// Download game from Steam Database
ipcMain.handle('download-game', async (event, appId, autoImport = false) => {
  try {
    const settings = await getAppSettings();
    const urlTemplate = settings.downloadUrlTemplate; // Fallback is included in the helper
    const url = urlTemplate.replace('{appId}', appId);

    // Use OS temp directory for portable compatibility
    const tempDir = os.tmpdir();
    const outputDir = path.join(tempDir, 'steam-delisi-temp');
    const zipPath = path.join(outputDir, `${appId}.zip`);
    const extractDir = path.join(outputDir, appId);
    
    // Ensure download directory exists
    await ensureDir(outputDir);
    
    console.log(`DEBUG: Starting download for app ${appId}`);
    console.log(`DEBUG: Download URL: ${url}`);
    console.log(`DEBUG: Output path: ${zipPath}`);
    
    // Download the zip file
    const downloadResult = await downloadFile(url, zipPath, (progress) => {
      // Send progress updates to renderer
      mainWindow.webContents.send('download-progress', {
        appId,
        progress: progress.percent,
        downloaded: progress.downloaded,
        total: progress.total,
        status: 'downloading'
      });
    });
    
    if (!downloadResult.success) {
      throw new Error(downloadResult.error);
    }
    
    console.log(`DEBUG: Download completed: ${zipPath}`);
    
    // Extract the zip file
    mainWindow.webContents.send('download-progress', {
      appId,
      progress: 100,
      status: 'extracting'
    });
    
    await extractZip(zipPath, extractDir);
    console.log(`DEBUG: Extraction completed: ${extractDir}`);
    
    // Clean up zip file
    await fs.unlink(zipPath);
    console.log(`DEBUG: Cleaned up zip file: ${zipPath}`);
    
    // Get list of extracted files
    const extractedFiles = await getExtractedFiles(extractDir);
    console.log(`DEBUG: Extracted files:`, extractedFiles);
    
    let importResult = null;
    if (autoImport && extractedFiles.length > 0) {
      // Auto import to Steam
      mainWindow.webContents.send('download-progress', {
        appId,
        progress: 100,
        status: 'importing'
      });
      
      importResult = await importExtractedFiles(extractedFiles);
      console.log(`DEBUG: Auto import result:`, importResult);
    }
    
    mainWindow.webContents.send('download-progress', {
      appId,
      progress: 100,
      status: 'completed'
    });
    
    // Clean up extracted files after a delay (to allow import if needed)
    setTimeout(async () => {
      try {
        if (await fileExists(extractDir)) {
          await fs.rmdir(extractDir, { recursive: true });
          console.log(`DEBUG: Cleaned up temp directory: ${extractDir}`);
        }
      } catch (error) {
        console.error('DEBUG: Error cleaning up temp directory:', error);
      }
    }, autoImport ? 5000 : 300000); // 5 seconds if auto-imported, 5 minutes if manual
    
    return {
      success: true,
      appId,
      extractedFiles,
      extractDir,
      importResult
    };
    
  } catch (error) {
    console.error('DEBUG: Download error:', error);
    mainWindow.webContents.send('download-progress', {
      appId,
      progress: 0,
      status: 'error',
      error: error.message
    });
    throw error;
  }
});

// Download file with progress tracking
function downloadFile(url, outputPath, progressCallback) {
  return new Promise((resolve, reject) => {
    const request = https.get(url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
        return;
      }
      
      const totalSize = parseInt(response.headers['content-length'] || '0');
      let downloadedSize = 0;
      
      const writeStream = require('fs').createWriteStream(outputPath);
      
      response.on('data', (chunk) => {
        downloadedSize += chunk.length;
        writeStream.write(chunk);
        
        if (progressCallback && totalSize > 0) {
          const percent = Math.round((downloadedSize / totalSize) * 100);
          progressCallback({
            percent,
            downloaded: downloadedSize,
            total: totalSize
          });
        }
      });
      
      response.on('end', () => {
        writeStream.end();
        resolve({ success: true });
      });
      
      response.on('error', (error) => {
        writeStream.destroy();
        reject(error);
      });
      
      writeStream.on('error', (error) => {
        reject(error);
      });
    });
    
    request.on('error', (error) => {
      reject(error);
    });
    
    request.setTimeout(30000, () => {
      request.destroy();
      reject(new Error('Download timeout'));
    });
  });
}

// Extract ZIP file (simple implementation using built-in modules)
async function extractZip(zipPath, extractDir) {
  // For now, we'll use a simple approach
  // In a production app, you might want to use a proper ZIP library
  await ensureDir(extractDir);
  
  // Use PowerShell to extract on Windows
  const { exec } = require('child_process');
  const util = require('util');
  const execAsync = util.promisify(exec);
  
  try {
    const command = `powershell -command "Expand-Archive -Path '${zipPath}' -DestinationPath '${extractDir}' -Force"`;
    await execAsync(command);
    console.log(`DEBUG: ZIP extracted successfully using PowerShell`);
  } catch (error) {
    console.error('DEBUG: PowerShell extraction failed, trying alternative method');
    throw new Error('ZIP extraction failed: ' + error.message);
  }
}

// Get list of extracted files
async function getExtractedFiles(extractDir) {
  const files = [];
  
  async function scanDirectory(dir) {
    const items = await fs.readdir(dir);
    
    for (const item of items) {
      const itemPath = path.join(dir, item);
      const stats = await fs.stat(itemPath);
      
      if (stats.isFile()) {
        const ext = path.extname(item).toLowerCase();
        if (ext === '.lua' || ext === '.manifest') {
          files.push({
            path: itemPath,
            name: item,
            size: stats.size,
            type: ext.slice(1) // Remove the dot
          });
        }
      } else if (stats.isDirectory()) {
        await scanDirectory(itemPath);
      }
    }
  }
  
  await scanDirectory(extractDir);
  return files;
}

// Import extracted files to Steam
async function importExtractedFiles(files) {
  try {
    const steamPath = findSteamPath();
    if (!steamPath) throw new Error('Steam yolu bulunamadı!');
    
    const depotcachePath = path.join(steamPath, 'depotcache');
    const stplugPath = path.join(steamPath, 'config', 'stplug-in');
    
    await ensureDir(depotcachePath);
    await ensureDir(stplugPath);
    
    let copiedFiles = 0;
    let manifestFiles = [];
    let luaFiles = [];
    
    for (const file of files) {
      const fileName = path.basename(file.path);
      const fileExt = path.extname(fileName).toLowerCase();
      
      if (fileExt === '.lua') {
        const dst = path.join(stplugPath, fileName);
        await fs.copyFile(file.path, dst);
        copiedFiles++;
        luaFiles.push(fileName);
        console.log(`DEBUG: Copied Lua file: ${fileName}`);
        
      } else if (fileExt === '.manifest') {
        // Copy to both locations
        const mainDepotcacheDst = path.join(depotcachePath, fileName);
        const configDepotcachePath = path.join(steamPath, 'config', 'depotcache');
        const configDepotcacheDst = path.join(configDepotcachePath, fileName);
        
        await ensureDir(configDepotcachePath);
        
        await fs.copyFile(file.path, mainDepotcacheDst);
        await fs.copyFile(file.path, configDepotcacheDst);
        copiedFiles += 2;
        manifestFiles.push(fileName);
        console.log(`DEBUG: Copied manifest file to both locations: ${fileName}`);
      }
    }
    
    // Clean up temp files after successful import
    if (files.length > 0) {
      const tempDir = path.dirname(files[0].path);
      setTimeout(async () => {
        try {
          if (await fileExists(tempDir)) {
            await fs.rmdir(tempDir, { recursive: true });
            console.log(`DEBUG: Cleaned up temp directory after import: ${tempDir}`);
          }
        } catch (error) {
          console.error('DEBUG: Error cleaning up temp directory after import:', error);
        }
      }, 2000); // 2 seconds delay
    }
    
    return {
      success: true,
      copiedFiles,
      manifestFiles,
      luaFiles
    };
    
  } catch (error) {
    console.error('DEBUG: Import extracted files error:', error);
    throw error;
  }
}

// Simple Authentication IPC Handlers (Firestore only)
ipcMain.handle('auth-login', async (event, loginData) => {
  try {
    const { identifier, password } = loginData;
    let userId = null;
    
    // Check if identifier is username or email
    if (identifier.includes('@')) {
      // Email login
      const emailDoc = await getDoc(doc(db, 'emails', identifier));
      if (!emailDoc.exists()) {
        throw new Error('E-posta adresi bulunamadı!');
      }
      userId = emailDoc.data().userId;
    } else {
      // Username login
      const usernameDoc = await getDoc(doc(db, 'usernames', identifier));
      if (!usernameDoc.exists()) {
        throw new Error('Kullanıcı adı bulunamadı!');
      }
      userId = usernameDoc.data().userId;
    }
    
    // Get user data
    const userDoc = await getDoc(doc(db, 'users', userId));
    if (!userDoc.exists()) {
      throw new Error('Kullanıcı verisi bulunamadı!');
    }
    
    const userData = userDoc.data();
    
    // Check password (basit kontrol - gerçek uygulamada hash kullanılmalı)
    if (userData.password !== password) {
      throw new Error('Şifre yanlış!');
    }
    
    // Generate new session token for this login (this will invalidate all other sessions)
    const sessionToken = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 16);
    const loginTimestamp = new Date().toISOString();
    
    // Get system information for device tracking
    const os = require('os');
    const { exec } = require('child_process');
    const util = require('util');
    const execAsync = util.promisify(exec);
    
    // Get detailed Windows version
    let detailedOS = `${os.platform()} ${os.release()}`;
    if (os.platform() === 'win32') {
      try {
        const { stdout } = await execAsync('wmic os get Caption /value');
        const caption = stdout.match(/Caption=(.+)/);
        if (caption && caption[1]) {
          detailedOS = caption[1].trim();
        }
      } catch (error) {
        console.log('Could not get detailed Windows version');
      }
    }
    
    // Get IP address
    let currentIP = 'Unknown';
    try {
      const networkInterfaces = os.networkInterfaces();
      for (const interfaceName in networkInterfaces) {
        const interfaces = networkInterfaces[interfaceName];
        for (const iface of interfaces) {
          // Skip internal and IPv6 addresses
          if (!iface.internal && iface.family === 'IPv4') {
            currentIP = iface.address;
            break;
          }
        }
        if (currentIP !== 'Unknown') break;
      }
    } catch (error) {
      console.log('Could not get IP address');
    }
    
    const deviceInfo = {
      hostname: os.hostname(),
      platform: os.platform(),
      arch: os.arch(),
      type: os.type(),
      release: os.release(),
      detailedOS: detailedOS,
      userAgent: 'Steam Delisi Desktop App'
    };
    
    // Get current user data to check for IP history
    const currentUserDoc = await getDoc(doc(db, 'users', userId));
    const currentUserData = currentUserDoc.data();
    
    // Track IP history
    let ipHistory = currentUserData.ipHistory || [];
    const ipEntry = {
      ip: currentIP,
      timestamp: loginTimestamp,
      device: deviceInfo.hostname,
      platform: detailedOS
    };
    
    // Add new IP if not already in history
    const existingIP = ipHistory.find(entry => entry.ip === currentIP);
    if (!existingIP) {
      ipHistory.push(ipEntry);
      // Keep only last 10 IP entries
      if (ipHistory.length > 10) {
        ipHistory = ipHistory.slice(-10);
      }
    } else {
      // Update existing IP entry
      existingIP.timestamp = loginTimestamp;
      existingIP.device = deviceInfo.hostname;
      existingIP.platform = detailedOS;
    }
    
    // Auto-clean IP history (older than 30 days)
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    ipHistory = ipHistory.filter(entry => new Date(entry.timestamp) > thirtyDaysAgo);

    // Check for suspicious activity (different IP)
    const hasMultipleIPs = ipHistory.length > 1;
    const suspiciousLogin = hasMultipleIPs && !existingIP;
    
    // Update last login and session token (this automatically logs out other devices)
    await updateDoc(doc(db, 'users', userId), {
      lastLogin: loginTimestamp,
      sessionToken: sessionToken,
      loginDevice: deviceInfo.hostname || `Device_${Date.now()}`,
      loginPlatform: detailedOS,
      loginIP: currentIP,
      deviceInfo: deviceInfo,
      loginLocation: 'Desktop App',
      ipHistory: ipHistory,
      suspiciousLogin: suspiciousLogin,
      lastIPCount: ipHistory.length
    });
    
    return {
      success: true,
      user: {
        id: userData.id,
        email: userData.email,
        username: userData.username,
        role: userData.role,
        sessionToken: sessionToken
      }
    };
    
  } catch (error) {
    console.error('Login error:', error);
    return {
      success: false,
      error: error.message
    };
  }
});

ipcMain.handle('auth-logout', async () => {
  try {
    // Basit logout - sadece frontend'de session temizlenir
    return { success: true };
  } catch (error) {
    console.error('Logout error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('auth-get-current-user', async () => {
  try {
    // Basit sistem - frontend'den gelen kullanıcı bilgilerini kontrol et
    // Gerçek uygulamada session token kontrolü yapılmalı
    return { success: false, user: null };
  } catch (error) {
    console.error('Get current user error:', error);
    return { success: false, user: null };
  }
});

// Admin Panel IPC Handlers
ipcMain.handle('admin-create-user', async (event, userData) => {
  try {
    const { username, email, password } = userData;
    
    // Check if username already exists
    const usernameDoc = await getDoc(doc(db, 'usernames', username));
    if (usernameDoc.exists()) {
      throw new Error('Bu kullanıcı adı zaten kullanımda!');
    }
    
    // Check if email already exists
    const emailDoc = await getDoc(doc(db, 'emails', email));
    if (emailDoc.exists()) {
      throw new Error('Bu e-posta adresi zaten kullanımda!');
    }
    
    // Generate unique user ID and initial session token
    const userId = 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    const initialSessionToken = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 16);
    
    // Save user data to Firestore
    await setDoc(doc(db, 'users', userId), {
      id: userId,
      email: email,
      username: username,
      password: password, // Gerçek uygulamada hash'lenmelidir
      role: 'user',
      createdAt: new Date().toISOString(),
      lastLogin: null,
      sessionToken: initialSessionToken
    });
    
    // Create username index
    await setDoc(doc(db, 'usernames', username), {
      userId: userId,
      email: email
    });
    
    // Create email index
    await setDoc(doc(db, 'emails', email), {
      userId: userId,
      username: username
    });
    
    return {
      success: true,
      user: {
        id: userId,
        email: email,
        username: username,
        role: 'user'
      }
    };
    
  } catch (error) {
    console.error('Create user error:', error);
    return {
      success: false,
      error: error.message
    };
  }
});

ipcMain.handle('admin-get-users', async () => {
  try {
    const usersSnapshot = await getDocs(collection(db, 'users'));
    const users = [];
    
    usersSnapshot.forEach((doc) => {
      const userData = doc.data();
      users.push({
        id: userData.id,
        email: userData.email,
        username: userData.username,
        role: userData.role,
        createdAt: userData.createdAt,
        lastLogin: userData.lastLogin,
        loginDevice: userData.loginDevice || 'Bilinmiyor',
        loginPlatform: userData.loginPlatform || 'Bilinmiyor',
        loginIP: userData.loginIP || 'Bilinmiyor',
        deviceInfo: userData.deviceInfo || {},
        ipHistory: userData.ipHistory || [],
        suspiciousLogin: userData.suspiciousLogin || false,
        lastIPCount: userData.lastIPCount || 0
      });
    });
    
    return {
      success: true,
      users: users
    };
    
  } catch (error) {
    console.error('Get users error:', error);
    return {
      success: false,
      error: error.message
    };
  }
});

// Get App Settings handler (for admin panel)
ipcMain.handle('admin-get-settings', async () => {
  try {
    // We use the same helper, which includes the default fallback
    const settings = await getAppSettings();
    return { success: true, settings };
  } catch (error) {
    console.error('Admin get settings error:', error);
    return { success: false, error: 'Ayarlar alınamadı: ' + error.message };
  }
});

// Update App Settings handler (for admin panel)
ipcMain.handle('admin-update-settings', async (event, newSettings) => {
  try {
    if (!newSettings || typeof newSettings.downloadUrlTemplate !== 'string' || !newSettings.downloadUrlTemplate.includes('{appId}')) {
      throw new Error('Geçersiz URL şablonu. Şablon "{appId}" metnini içermelidir.');
    }

    // Use setDoc with merge to only update provided fields
    await setDoc(doc(db, 'settings', 'app_config'), {
      downloadUrlTemplate: newSettings.downloadUrlTemplate
    }, { merge: true });

    return { success: true, message: 'Veritabanı URL\'si başarıyla güncellendi!' };
  } catch (error) {
    console.error('Admin update settings error:', error);
    return { success: false, error: 'Ayarlar güncellenemedi: ' + error.message };
  }
});

// Change password handler
ipcMain.handle('change-password', async (event, data) => {
  try {
    const { currentPassword, newPassword, currentUser } = data;
    
    // Check if current user data is provided
    if (!currentUser || !currentUser.id) {
      return { success: false, error: 'Kullanıcı oturumu bulunamadı!' };
    }
    
    // Get user document
    const userDoc = await getDoc(doc(db, 'users', currentUser.id));
    if (!userDoc.exists()) {
      return { success: false, error: 'Kullanıcı bulunamadı!' };
    }
    
    const userData = userDoc.data();
    
    // Verify current password
    if (userData.password !== currentPassword) {
      return { success: false, error: 'Mevcut şifre yanlış!' };
    }
    
    // Generate new session token to invalidate all other sessions
    const newSessionToken = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 16);
    
    // Update password and session token
    await updateDoc(doc(db, 'users', currentUser.id), {
      password: newPassword,
      sessionToken: newSessionToken,
      lastLogin: new Date().toISOString()
    });
    
    return {
      success: true,
      message: 'Şifre başarıyla değiştirildi!',
      newSessionToken: newSessionToken
    };
    
  } catch (error) {
    console.error('Change password error:', error);
    return { success: false, error: 'Şifre değiştirme sırasında bir hata oluştu!' };
  }
});

// Get current user info handler
ipcMain.handle('get-current-user', async (event, userData) => {
  try {
    if (!userData || !userData.id) {
      return { success: false, error: 'Kullanıcı bilgisi bulunamadı!' };
    }
    
    // Get fresh user data from database
    const userDoc = await getDoc(doc(db, 'users', userData.id));
    if (!userDoc.exists()) {
      return { success: false, error: 'Kullanıcı bulunamadı!' };
    }
    
    const freshUserData = userDoc.data();
    
    return {
      success: true,
      user: {
        id: freshUserData.id,
        username: freshUserData.username,
        email: freshUserData.email,
        role: freshUserData.role,
        createdAt: freshUserData.createdAt,
        lastLogin: freshUserData.lastLogin,
        sessionToken: freshUserData.sessionToken,
        loginDevice: freshUserData.loginDevice,
        loginPlatform: freshUserData.loginPlatform,
        loginIP: freshUserData.loginIP,
        deviceInfo: freshUserData.deviceInfo
      }
    };
    
  } catch (error) {
    console.error('Get current user error:', error);
    return { success: false, error: 'Kullanıcı bilgileri alınamadı!' };
  }
});

// Validate session token handler
ipcMain.handle('validate-session', async (event, userData) => {
  try {
    if (!userData || !userData.id || !userData.sessionToken) {
      return { success: false, error: 'Oturum bilgisi eksik!' };
    }
    
    // Get current user data from database
    const userDoc = await getDoc(doc(db, 'users', userData.id));
    if (!userDoc.exists()) {
      return { success: false, error: 'Kullanıcı bulunamadı!' };
    }
    
    const currentUserData = userDoc.data();
    
    // Check if session token matches
    if (currentUserData.sessionToken !== userData.sessionToken) {
      return { success: false, error: 'Oturum geçersiz! Başka bir cihazdan şifre değiştirilmiş olabilir.' };
    }
    
    return { success: true, message: 'Oturum geçerli' };
    
  } catch (error) {
    console.error('Validate session error:', error);
    return { success: false, error: 'Oturum doğrulama hatası!' };
  }
});

ipcMain.handle('admin-update-user-password', async (event, updateData) => {
  try {
    const { userId, newPassword } = updateData;
    
    // Update password in Firestore
    await updateDoc(doc(db, 'users', userId), {
      password: newPassword // Gerçek uygulamada hash'lenmelidir
    });
    
    return {
      success: true,
      message: 'Şifre başarıyla güncellendi!'
    };
    
  } catch (error) {
    console.error('Update password error:', error);
    return {
      success: false,
      error: error.message
    };
  }
});

ipcMain.handle('admin-clear-ip-history', async (event, { userId }) => {
  try {
    if (!userId) {
      throw new Error('Kullanıcı ID\'si belirtilmedi!');
    }

    await updateDoc(doc(db, 'users', userId), {
      ipHistory: [],
      suspiciousLogin: false,
      lastIPCount: 0
    });

    return { success: true, message: 'IP geçmişi başarıyla temizlendi!' };
  } catch (error) {
    console.error('Clear IP history error:', error);
    return { success: false, error: error.message };
  }
});

// Delete user handler
ipcMain.handle('admin-delete-user', async (event, deleteData) => {
  try {
    const { userId, username, email } = deleteData;
    
    // Delete user document
    await deleteDoc(doc(db, 'users', userId));
    
    // Delete username index
    await deleteDoc(doc(db, 'usernames', username));
    
    // Delete email index
    await deleteDoc(doc(db, 'emails', email));
    
    return {
      success: true,
      message: 'Kullanıcı başarıyla silindi!'
    };
    
  } catch (error) {
    console.error('Delete user error:', error);
    return {
      success: false,
      error: error.message
    };
  }
});