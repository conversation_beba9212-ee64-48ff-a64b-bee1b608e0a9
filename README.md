# Steam Delisi

Modern Electron-based application for managing Steam Lua scripts and manifest files. Export, import, and manage your Steam game files with an intuitive drag-and-drop interface.

![Steam Delisi](logo.png)

## Features

### 🎮 Game Management
- **Export Games**: Create Lua scripts and copy manifest files for any Steam game
- **Import Games**: Drag & drop Lua and manifest files to import games
- **Installed Games**: View and manage all currently installed games
- **Game Preview**: Automatic game detection and preview before import

### 🖥️ Modern Interface
- **Three-Tab Layout**: Export, Import, and Games management
- **Drag & Drop**: Intuitive file handling with visual feedback
- **Modal Previews**: Game information popups without scrolling
- **Steam Integration**: Automatic Steam path detection and process management

### ⚡ Advanced Features
- **Steam API Integration**: Automatic game name and cover image fetching
- **Batch Operations**: Handle multiple files simultaneously
- **Real-time Feedback**: Toast notifications and progress indicators
- **Portable**: Single executable file, no installation required

## Installation

### Option 1: Download Portable Executable
1. Download the latest `Steam Lua & Manifest Exporter-X.X.X-portable.exe` from [Releases](../../releases)
2. Run the executable directly - no installation required

### Option 2: Build from Source
1. Clone this repository:
   ```bash
   git clone https://github.com/yourusername/steam-lua-exporter.git
   cd steam-lua-exporter
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Run in development mode:
   ```bash
   npm start
   ```

4. Build portable executable:
   ```bash
   npm run build-portable
   ```

## Usage

### Export Tab
1. Enter a Steam Game ID (e.g., `1971870`)
2. Choose manifest selection mode (newest or all)
3. Click "Tara" to scan for manifest files
4. Click "Lua Oluştur" to create Lua script
5. Click "Steam'e Aktar" to import directly to Steam

### Import Tab
1. Drag and drop `.lua` and `.manifest` files into the drop zone
2. Review the game preview popup showing which games will be imported
3. Click "Steam'e Aktar" to import files to Steam
4. Or click "Devam Et" to continue adding more files

### Games Tab
1. View all currently installed games with cover images
2. Export individual games using the "Export" button
3. Delete games using the "Sil" button
4. Refresh the list with "Oyunları Yenile"

## Technical Details

### Built With
- **Electron**: Cross-platform desktop application framework
- **Node.js**: Native modules only (no external dependencies)
- **Modern Web Technologies**: HTML5, CSS3, ES6+

### Architecture
- **Main Process**: Steam integration, file operations, API calls
- **Renderer Process**: UI logic, drag & drop, user interactions
- **IPC Communication**: Secure inter-process communication

### Steam Integration
- Automatic Steam path detection
- Steam API integration for game metadata
- Steam process management (restart functionality)
- Manifest and Lua file management

## Development

### Prerequisites
- Node.js 16+ 
- npm or yarn
- Windows (for building Windows executables)

### Development Commands
```bash
# Start development server
npm start

# Build portable executable
npm run build-portable

# Build all targets
npm run build

# Clean cache
npm cache clean --force
```

### Project Structure
```
steam-lua-exporter/
├── main.js              # Electron main process
├── renderer.js          # UI logic and interactions
├── index.html           # Application layout
├── styles.css           # Modern styling
├── package.json         # Dependencies and build config
├── logo.png            # Application logo
├── memory-bank/        # Project documentation
└── dist/              # Build outputs (gitignored)
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Author

**İsmail Akçay**
- Website: [apkdelisi.net](https://apkdelisi.net)
- GitHub: [@yourusername](https://github.com/yourusername)

## Acknowledgments

- Steam for the gaming platform and API
- Electron team for the amazing framework
- All contributors and users of this tool

---

**Note**: This tool is for educational and personal use. Please respect Steam's Terms of Service and only use with games you own.