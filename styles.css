/* Modern CSS Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* CSS Variables for Dark Theme */
:root {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d30;
    --bg-tertiary: #3c3c3c;
    --bg-input: #1e1e1e;
    --accent: #0078d4;
    --accent-hover: #106ebe;
    --success: #16c60c;
    --warning: #ffcc02;
    --error: #f14c4c;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-muted: #999999;
    --border: #464647;
    --border-light: #555555;
    --shadow: rgba(0, 0, 0, 0.3);
    --gradient: linear-gradient(135deg, var(--accent), #005a9e);
    --border-radius: 8px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Base Styles */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
}

.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.app-header {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border);
    padding: 20px 30px;
    box-shadow: 0 2px 10px var(--shadow);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 15px;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 20px;
}

.header-btn {
    font-size: 12px;
    padding: 8px 16px;
}

.logo-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(0, 120, 212, 0.3);
    background: var(--gradient);
    padding: 8px;
}

.app-logo {
    width: 100%;
    height: 100%;
    object-fit: contain;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.app-title {
    font-size: 24px;
    font-weight: 700;
    background: var(--gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.author-info {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
}

.author-text {
    color: var(--text-secondary);
}

.author-link {
    color: var(--accent);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.author-link:hover {
    color: var(--accent-hover);
    text-decoration: underline;
}

/* Tab Navigation */
.tab-navigation {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border);
    padding: 0 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.tab-buttons {
    display: flex;
    gap: 2px;
}

/* User Info in Tab Navigation */
.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
    color: var(--text-secondary);
    font-size: 14px;
}

.user-welcome {
    color: var(--text-primary);
    font-weight: 500;
}

.logout-btn {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.2);
}

.logout-btn:hover {
    background: linear-gradient(135deg, #c82333, #bd2130);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

.logout-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.2);
}

/* Footer */
.app-footer {
    background: var(--bg-secondary);
    border-top: 1px solid var(--border);
    padding: 15px 30px;
    margin-top: auto;
}

.footer-content {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    color: var(--text-secondary);
    font-size: 14px;
}

.footer-link {
    color: var(--accent);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.footer-link:hover {
    color: var(--accent-hover);
    text-decoration: underline;
}

.tab-button {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    padding: 15px 25px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
    position: relative;
}

.tab-button:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.tab-button.active {
    background: var(--bg-primary);
    color: var(--accent);
    border-bottom: 2px solid var(--accent);
}

.tab-icon {
    font-size: 16px;
}

/* Main Content */
.main-content {
    flex: 1;
    background: var(--bg-primary);
}

.tab-content {
    display: none;
    height: 100%;
    overflow-y: auto;
    padding: 30px;
}

.tab-content.active {
    display: block;
}

/* Export Section */
.export-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    height: 100%;
}

.input-panel {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: 30px;
    border: 1px solid var(--border);
    box-shadow: 0 4px 20px var(--shadow);
}

.input-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 25px;
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.input-group label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
}

.input-group input[type="text"] {
    background: var(--bg-input);
    border: 1px solid var(--border);
    border-radius: var(--border-radius);
    padding: 12px 16px;
    color: var(--text-primary);
    font-size: 14px;
    transition: var(--transition);
    width: 100%;
}

.input-group input[type="text"]:focus {
    outline: none;
    border-color: var(--accent);
    box-shadow: 0 0 0 3px rgba(0, 120, 212, 0.1);
}

.radio-group {
    display: flex;
    gap: 20px;
}

.radio-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 14px;
    color: var(--text-secondary);
    transition: var(--transition);
}

.radio-label:hover {
    color: var(--text-primary);
}

.radio-label input[type="radio"] {
    display: none;
}

.radio-custom {
    width: 18px;
    height: 18px;
    border: 2px solid var(--border);
    border-radius: 50%;
    position: relative;
    transition: var(--transition);
}

.radio-label input[type="radio"]:checked + .radio-custom {
    border-color: var(--accent);
}

.radio-label input[type="radio"]:checked + .radio-custom::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    background: var(--accent);
    border-radius: 50%;
}

/* Buttons */
.action-buttons {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
    flex-wrap: wrap;
}

.btn {
    background: var(--bg-tertiary);
    border: 1px solid var(--border);
    color: var(--text-primary);
    padding: 12px 20px;
    border-radius: var(--border-radius);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    white-space: nowrap;
}

.btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px var(--shadow);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

.btn-primary {
    background: var(--accent);
    border-color: var(--accent);
}

.btn-primary:hover:not(:disabled) {
    background: var(--accent-hover);
    border-color: var(--accent-hover);
}

.btn-success {
    background: var(--success);
    border-color: var(--success);
}

.btn-success:hover:not(:disabled) {
    background: #14a00a;
    border-color: #14a00a;
}

.btn-warning {
    background: linear-gradient(135deg, #66c0f4 0%, #4a90c2 100%);
    border-color: #66c0f4;
    color: white;
    box-shadow: 0 4px 15px rgba(102, 192, 244, 0.3);
}

.btn-warning:hover:not(:disabled) {
    background: linear-gradient(135deg, #4a90c2 0%, #2a5885 100%);
    border-color: #4a90c2;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 192, 244, 0.4);
}

.btn-danger {
    background: var(--error);
    border-color: var(--error);
}

.btn-danger:hover:not(:disabled) {
    background: #d63939;
    border-color: #d63939;
}

.btn-secondary {
    background: var(--bg-tertiary);
    border-color: var(--border);
}

.btn-small {
    padding: 8px 16px;
    font-size: 12px;
}

.btn-icon {
    font-size: 16px;
}

/* Folder Inputs */
.folder-inputs {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.folder-input {
    display: flex;
    gap: 10px;
    align-items: center;
}

.folder-input input {
    flex: 1;
}

.folder-status {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: var(--border-radius);
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
    transition: var(--transition);
}

.folder-status.checking {
    background: rgba(255, 204, 2, 0.1);
    color: var(--warning);
    border: 1px solid rgba(255, 204, 2, 0.3);
}

.folder-status.success {
    background: rgba(22, 198, 12, 0.1);
    color: var(--success);
    border: 1px solid rgba(22, 198, 12, 0.3);
}

.folder-status.error {
    background: rgba(241, 76, 76, 0.1);
    color: var(--error);
    border: 1px solid rgba(241, 76, 76, 0.3);
}

.status-icon {
    font-size: 14px;
}

.status-text {
    font-size: 11px;
}

/* Results Panel */
.results-panel {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border);
    box-shadow: 0 4px 20px var(--shadow);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.results-header {
    padding: 20px 25px;
    border-bottom: 1px solid var(--border);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--bg-tertiary);
}

.results-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

.log-container {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: var(--bg-input);
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 13px;
    line-height: 1.5;
}

.log-message {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    margin-bottom: 8px;
    padding: 8px 12px;
    border-radius: 6px;
    transition: var(--transition);
}

.log-message:hover {
    background: var(--bg-secondary);
}

.log-time {
    color: var(--text-muted);
    font-size: 11px;
    min-width: 70px;
}

.log-icon {
    font-size: 14px;
    min-width: 20px;
}

.log-text {
    flex: 1;
    word-wrap: break-word;
}

.log-message.success {
    border-left: 3px solid var(--success);
}

.log-message.error {
    border-left: 3px solid var(--error);
}

.log-message.warning {
    border-left: 3px solid var(--warning);
}

.log-message.info {
    border-left: 3px solid var(--accent);
}

/* Games Section */
.games-section {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.games-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--border);
}

.games-header h2 {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-primary);
}

.games-header-actions {
    display: flex;
    gap: 15px;
    align-items: center;
}

/* Import Section */
.import-section {
    padding: 30px;
    max-width: 1000px;
    margin: 0 auto;
}

.import-header {
    text-align: center;
    margin-bottom: 40px;
}

.import-header h2 {
    font-size: 28px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 10px;
}

.import-header p {
    font-size: 16px;
    color: var(--text-secondary);
}

/* Drop Zone */
.drop-zone {
    position: relative;
    border: 3px dashed var(--border);
    border-radius: var(--border-radius);
    background: var(--bg-secondary);
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    margin-bottom: 30px;
}

.drop-zone.drag-over {
    border-color: var(--accent);
    background: var(--bg-tertiary);
    transform: scale(1.02);
}

.drop-zone-content {
    text-align: center;
    padding: 40px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.drop-icon {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.7;
}

.drop-zone-content h3 {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 10px;
}

.drop-zone-content p {
    font-size: 16px;
    color: var(--text-secondary);
    margin-bottom: 20px;
}

.drop-zone-or {
    font-size: 14px;
    color: var(--text-muted);
    margin: 20px 0;
    position: relative;
}

.drop-zone-or::before,
.drop-zone-or::after {
    content: '';
    position: absolute;
    top: 50%;
    width: 60px;
    height: 1px;
    background: var(--border);
}

.drop-zone-or::before {
    left: -80px;
}

.drop-zone-or::after {
    right: -80px;
}

/* Drop Overlay */
.drop-zone-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 120, 212, 0.9);
    border-radius: var(--border-radius);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.drop-zone-overlay.active {
    display: flex;
}

.drop-overlay-content {
    text-align: center;
    color: white;
}

.drop-overlay-icon {
    font-size: 72px;
    margin-bottom: 20px;
    animation: bounce 1s infinite;
}

.drop-overlay-content h3 {
    font-size: 28px;
    font-weight: 700;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* Import Files List */
.import-files-list {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border);
    margin-bottom: 30px;
}

.import-files-header {
    padding: 20px 25px;
    border-bottom: 1px solid var(--border);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--bg-tertiary);
}

.import-files-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

.files-container {
    padding: 20px;
}

.file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    background: var(--bg-input);
    border-radius: var(--border-radius);
    margin-bottom: 10px;
    border: 1px solid var(--border);
    transition: var(--transition);
}

.file-item:hover {
    border-color: var(--accent);
}

.file-info {
    display: flex;
    align-items: center;
    gap: 15px;
    flex: 1;
}

.file-icon {
    font-size: 24px;
    width: 40px;
    text-align: center;
}

.file-details h4 {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.file-details p {
    font-size: 12px;
    color: var(--text-secondary);
}

.file-type {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
}

.file-type.lua {
    background: var(--accent);
    color: white;
}

.file-type.manifest {
    background: var(--warning);
    color: var(--bg-primary);
}

.file-remove {
    background: var(--error);
    border: none;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.file-remove:hover {
    background: #d63939;
    transform: scale(1.1);
}

.import-actions {
    padding: 20px 25px;
    border-top: 1px solid var(--border);
    text-align: center;
}

/* Import Info */
.import-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.info-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border);
    border-radius: var(--border-radius);
    padding: 20px;
}

.info-card h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.info-card ul {
    list-style: none;
    padding: 0;
}

.info-card li {
    font-size: 14px;
    color: var(--text-secondary);
    margin-bottom: 8px;
    padding-left: 20px;
    position: relative;
}

.info-card li::before {
    content: '•';
    position: absolute;
    left: 0;
    color: var(--accent);
    font-weight: bold;
}

/* Download Section */
.download-section {
    padding: 30px;
    max-width: 1000px;
    margin: 0 auto;
}

.download-header {
    text-align: center;
    margin-bottom: 40px;
}

.download-header h2 {
    font-size: 28px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 10px;
}

.download-header p {
    font-size: 16px;
    color: var(--text-secondary);
}

.download-panel {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border);
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 4px 20px var(--shadow);
}

.download-input-section {
    margin-bottom: 30px;
}


/* Autocomplete Styles */
.autocomplete-container {
    position: relative;
    width: 100%;
}

.autocomplete-container input {
    width: 100%;
}

.autocomplete-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--bg-secondary);
    border: 1px solid var(--border);
    border-top: none;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
    box-shadow: 0 4px 20px var(--shadow);
}

.autocomplete-dropdown.active {
    display: block;
}

.autocomplete-item {
    padding: 12px 16px;
    cursor: pointer;
    border-bottom: 1px solid var(--border);
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 12px;
}

.autocomplete-item:last-child {
    border-bottom: none;
}

.autocomplete-item:hover,
.autocomplete-item.selected {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.autocomplete-item-image {
    width: 40px;
    height: 20px;
    border-radius: 4px;
    overflow: hidden;
    flex-shrink: 0;
    background: var(--bg-input);
    display: flex;
    align-items: center;
    justify-content: center;
}

.autocomplete-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.autocomplete-item-info {
    flex: 1;
    min-width: 0;
}

.autocomplete-item-name {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.autocomplete-item-id {
    font-size: 12px;
    color: var(--text-secondary);
    font-family: monospace;
}

.autocomplete-loading {
    padding: 20px;
    text-align: center;
    color: var(--text-secondary);
    font-size: 14px;
}

.autocomplete-loading .spinner {
    width: 20px;
    height: 20px;
    margin: 0 auto 10px auto;
}

.autocomplete-no-results {
    padding: 20px;
    text-align: center;
    color: var(--text-muted);
    font-size: 14px;
    font-style: italic;
}

.download-actions {
    display: flex;
    gap: 20px;
    margin-top: 25px;
    flex-wrap: wrap;
    justify-content: center;
}

.download-actions .btn {
    min-width: 180px;
    padding: 15px 25px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 10px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.download-actions .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.download-actions .btn:hover::before {
    left: 100%;
}

.download-actions .btn:hover:not(:disabled) {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.download-actions .btn:active {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.download-actions .btn-primary {
    background: linear-gradient(135deg, var(--accent), var(--accent-hover));
    border: none;
    color: white;
    box-shadow: 0 4px 15px rgba(0, 120, 212, 0.3);
}

.download-actions .btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--accent-hover), #0056b3);
    box-shadow: 0 8px 25px rgba(0, 120, 212, 0.4);
}

.download-actions .btn-success {
    background: linear-gradient(135deg, var(--success), #14a00a);
    border: none;
    color: white;
    box-shadow: 0 4px 15px rgba(22, 198, 12, 0.3);
}

.download-actions .btn-success:hover:not(:disabled) {
    background: linear-gradient(135deg, #14a00a, #0f7a07);
    box-shadow: 0 8px 25px rgba(22, 198, 12, 0.4);
}

.download-actions .btn .btn-icon {
    font-size: 18px;
    margin-right: 8px;
}

/* Game Preview Styles */
.game-preview {
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, rgba(0, 120, 212, 0.1), rgba(0, 120, 212, 0.05));
    border: 1px solid rgba(0, 120, 212, 0.2);
    border-radius: 10px;
    padding: 15px;
    margin: 15px 0;
    gap: 15px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.game-preview:hover {
    background: linear-gradient(135deg, rgba(0, 120, 212, 0.15), rgba(0, 120, 212, 0.08));
    border-color: rgba(0, 120, 212, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.game-preview-image {
    flex-shrink: 0;
    width: 120px;
    height: 60px;
    border-radius: 6px;
    overflow: hidden;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.game-preview-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 6px;
    transition: transform 0.2s ease;
}

.game-preview-image img:hover {
    transform: scale(1.05);
}

.game-preview-info {
    flex: 1;
    min-width: 0;
}

.game-preview-info h4 {
    margin: 0 0 5px 0;
    color: #ffffff;
    font-size: 15px;
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.game-preview-info p {
    margin: 0;
    color: rgba(0, 120, 212, 0.9);
    font-size: 12px;
    font-weight: 500;
    background: rgba(0, 120, 212, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
    display: inline-block;
}

/* Download Progress */
.download-progress {
    background: var(--bg-tertiary);
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 20px;
    border: 1px solid var(--border);
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.progress-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

.progress-status {
    font-size: 14px;
    color: var(--text-secondary);
}

.progress-bar {
    background: var(--bg-input);
    border-radius: 10px;
    height: 8px;
    overflow: hidden;
    margin-bottom: 10px;
    border: 1px solid var(--border);
}

.progress-fill {
    background: linear-gradient(90deg, var(--accent), var(--accent-hover));
    height: 100%;
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 10px;
}

.progress-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
}

.progress-text {
    color: var(--text-primary);
    font-weight: 600;
}

.progress-size {
    color: var(--text-secondary);
}

.progress-actions {
    margin-top: 15px;
    text-align: center;
    padding-top: 15px;
    border-top: 1px solid var(--border);
}

.progress-actions .btn {
    padding: 10px 20px;
    font-size: 14px;
}

/* Download Results */
.download-results {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border);
    box-shadow: 0 4px 20px var(--shadow);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.download-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

/* Import Downloaded Game Button */
.import-downloaded-btn {
    display: inline-flex !important;
    align-items: center;
    gap: 4px;
    vertical-align: middle;
    border-radius: 4px !important;
    transition: var(--transition);
}

.import-downloaded-btn:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px var(--shadow);
}

.import-downloaded-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* Responsive */
@media (max-width: 768px) {
    .import-info,
    .download-info {
        grid-template-columns: 1fr;
    }
    
    .download-actions {
        flex-direction: column;
    }
    
    .drop-zone-or::before,
    .drop-zone-or::after {
        width: 40px;
    }
    
    .drop-zone-or::before {
        left: -60px;
    }
    
    .drop-zone-or::after {
        right: -60px;
    }
}

.games-grid {
    flex: 1;
    overflow-y: auto;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 20px;
    padding: 20px;
    max-height: calc(100vh - 250px);
    align-content: start;
    grid-auto-rows: max-content;
}

/* Game Cards */
.game-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border);
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: var(--transition);
    position: relative;
    min-height: 280px;
    height: auto;
}

.game-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px var(--shadow);
    border-color: var(--accent);
}

/* Game Image Section */
.game-card-image {
    position: relative;
    height: 150px;
    overflow: hidden;
    background: linear-gradient(135deg, var(--bg-tertiary), var(--bg-input));
}

.game-cover {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.game-card:hover .game-cover {
    transform: scale(1.05);
}

.game-cover-fallback {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--accent), #005a9e);
    color: white;
    text-align: center;
    padding: 20px;
}

.game-icon {
    font-size: 32px;
    margin-bottom: 10px;
}

.game-title-fallback {
    font-size: 14px;
    font-weight: 600;
    line-height: 1.3;
}

.game-status {
    position: absolute;
    top: 10px;
    right: 10px;
    background: var(--success);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    z-index: 2;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.game-card-body {
    padding: 15px;
}

.game-header {
    margin-bottom: 12px;
    border-bottom: 1px solid var(--border);
    padding-bottom: 8px;
}

.game-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    line-height: 1.3;
}

.game-info {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 20px;
}

.game-info-item {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
}

.info-icon {
    font-size: 16px;
    width: 20px;
    text-align: center;
}

.info-text {
    color: var(--text-secondary);
}

.info-value {
    color: var(--text-primary);
    font-weight: 500;
}

.game-actions {
    display: flex;
    gap: 10px;
}

.game-actions .btn {
    flex: 1;
    justify-content: center;
    padding: 10px 16px;
    font-size: 13px;
}

/* Loading Spinner */
.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: var(--text-secondary);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border);
    border-top: 3px solid var(--accent);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Empty State */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    color: var(--text-secondary);
    text-align: center;
}

.empty-state-icon {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-state h3 {
    font-size: 18px;
    margin-bottom: 10px;
    color: var(--text-primary);
}

.empty-state p {
    font-size: 14px;
    max-width: 400px;
    line-height: 1.6;
}

/* Game Preview Modal Styles */
.game-preview-modal {
    max-width: 600px;
    width: 100%;
}

.game-preview-modal .game-preview-header {
    margin-bottom: 20px;
    text-align: center;
}

.game-preview-modal .game-preview-header h3 {
    color: var(--accent);
    margin-bottom: 10px;
    font-size: 20px;
}

.game-preview-modal .game-preview-header p {
    color: var(--text-secondary);
    font-size: 14px;
}

.games-preview-list {
    max-height: 400px;
    overflow-y: auto;
    margin-bottom: 20px;
    padding: 10px;
    background: var(--bg-input);
    border-radius: var(--border-radius);
    border: 1px solid var(--border);
}

.game-preview-item {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: 15px;
    margin-bottom: 10px;
    border-left: 4px solid var(--border);
    transition: var(--transition);
}

.game-preview-item.complete {
    border-left-color: var(--success);
}

.game-preview-item.incomplete {
    border-left-color: var(--warning);
}

.game-preview-item .game-preview-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
    text-align: left;
}

.game-status-icon {
    font-size: 18px;
}

.game-name {
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
    margin: 0;
    flex: 1;
}

.game-preview-details {
    display: flex;
    gap: 15px;
    font-size: 12px;
    color: var(--text-secondary);
}

.game-id {
    background: var(--bg-tertiary);
    padding: 4px 8px;
    border-radius: 4px;
    font-family: monospace;
}

.lua-status {
    color: var(--success);
    font-weight: 500;
}

.manifest-status {
    font-weight: 500;
}


/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.toast {
    background: var(--bg-secondary);
    border: 1px solid var(--border);
    border-radius: var(--border-radius);
    padding: 15px 20px;
    min-width: 300px;
    box-shadow: 0 4px 20px var(--shadow);
    display: flex;
    align-items: center;
    gap: 12px;
    animation: slideIn 0.3s ease-out;
}

.toast.success {
    border-left: 4px solid var(--success);
}

.toast.error {
    border-left: 4px solid var(--error);
}

.toast.warning {
    border-left: 4px solid var(--warning);
}

.toast.info {
    border-left: 4px solid var(--accent);
}

.toast-icon {
    font-size: 18px;
}

.toast-message {
    flex: 1;
    font-size: 14px;
    color: var(--text-primary);
}

.toast-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 18px;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    backdrop-filter: blur(4px);
}

.modal-overlay.active {
    display: flex;
}

.modal {
    background: var(--bg-secondary);
    border: 1px solid var(--border);
    border-radius: var(--border-radius);
    min-width: 400px;
    max-width: 500px;
    box-shadow: 0 10px 40px var(--shadow);
    animation: modalIn 0.3s ease-out;
}

.modal-header {
    padding: 20px 25px;
    border-bottom: 1px solid var(--border);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 24px;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: var(--transition);
}

.modal-close:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.modal-body {
    padding: 25px;
}

.modal-body p {
    font-size: 14px;
    line-height: 1.6;
    color: var(--text-secondary);
}

.modal-footer {
    padding: 20px 25px;
    border-top: 1px solid var(--border);
    display: flex;
    justify-content: flex-end;
    gap: 15px;
}

@keyframes modalIn {
    from {
        transform: scale(0.9);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-input);
}

::-webkit-scrollbar-thumb {
    background: var(--border);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--border-light);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .export-section {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .games-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    }
}

/* User Info Styles */
.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 8px 16px;
    background: rgba(0, 120, 212, 0.1);
    border-radius: var(--border-radius);
    border: 1px solid rgba(0, 120, 212, 0.2);
}

.user-welcome {
    font-size: 14px;
    color: var(--text-primary);
    font-weight: 500;
}

.user-welcome span {
    color: var(--accent);
    font-weight: 600;
}

/* Profile Section Styles */
.profile-section {
    padding: 30px;
    max-width: 1000px;
    margin: 0 auto;
}

.profile-header {
    text-align: center;
    margin-bottom: 40px;
}

.profile-header h2 {
    font-size: 28px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 10px;
}

.profile-header p {
    font-size: 16px;
    color: var(--text-secondary);
}

.profile-panels {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.profile-panel {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border);
    padding: 30px;
    box-shadow: 0 4px 20px var(--shadow);
}

.profile-panel .panel-header {
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border);
}

.profile-panel .panel-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

/* User Info Display */
.user-info-display {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid var(--border);
}

.info-row:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 500;
    color: var(--text-secondary);
    font-size: 14px;
}

.info-value {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
}

/* Change Password Form */
.change-password-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.change-password-form .form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.change-password-form .form-group label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
}

.change-password-form .form-group input {
    background: var(--bg-input);
    border: 1px solid var(--border);
    border-radius: var(--border-radius);
    padding: 12px 16px;
    color: var(--text-primary);
    font-size: 14px;
    transition: var(--transition);
}

.change-password-form .form-group input:focus {
    outline: none;
    border-color: var(--accent);
    box-shadow: 0 0 0 3px rgba(0, 120, 212, 0.1);
}

.form-actions {
    margin-top: 10px;
    text-align: center;
}

/* Responsive Profile */
@media (max-width: 768px) {
    .profile-panels {
        grid-template-columns: 1fr;
    }
    
    .profile-section {
        padding: 20px;
    }
    
    .profile-panel {
        padding: 20px;
    }
}

/* Admin Panel Styles */
.admin-section {
    padding: 30px;
    max-width: 1200px;
    margin: 0 auto;
}

.admin-header {
    text-align: center;
    margin-bottom: 40px;
}

.admin-header h2 {
    font-size: 28px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 10px;
}

.admin-header p {
    font-size: 16px;
    color: var(--text-secondary);
}

.admin-panels {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.admin-panel {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border);
    padding: 30px;
    box-shadow: 0 4px 20px var(--shadow);
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--border);
}

.panel-header h3 {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
}

/* Add User Form */
.add-user-form {
    background: var(--bg-tertiary);
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 30px;
    border: 1px solid var(--border);
}

.add-user-form h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-row:last-child {
    margin-bottom: 0;
}

.add-user-form .input-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.add-user-form .input-group label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
}

.add-user-form .input-group input {
    background: var(--bg-input);
    border: 1px solid var(--border);
    border-radius: var(--border-radius);
    padding: 12px 16px;
    color: var(--text-primary);
    font-size: 14px;
    transition: var(--transition);
}

.add-user-form .input-group input:focus {
    outline: none;
    border-color: var(--accent);
    box-shadow: 0 0 0 3px rgba(0, 120, 212, 0.1);
}

.add-user-form .input-group input[type="email"] {
    font-family: inherit;
}

.add-user-form .input-group input[type="password"] {
    font-family: inherit;
}

/* Users List */
.users-list {
    margin-top: 20px;
}

.users-list h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.users-table-container {
    background: var(--bg-input);
    border-radius: var(--border-radius);
    border: 1px solid var(--border);
    overflow: hidden;
}

.users-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.users-table thead {
    background: var(--bg-tertiary);
}

.users-table th {
    padding: 15px 20px;
    text-align: left;
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border);
}

.users-table td {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border);
    color: var(--text-secondary);
}

.users-table tbody tr:hover {
    background: var(--bg-secondary);
}

.users-table tbody tr:last-child td {
    border-bottom: none;
}

/* Table Cell Styles */
.username-cell strong {
    color: var(--text-primary);
    font-weight: 600;
}

.email-cell {
    font-family: monospace;
    font-size: 13px;
}

.role-cell {
    text-align: center;
}

.role-badge {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.role-badge.role-admin {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: var(--bg-primary);
    box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
}

.role-badge.role-user {
    background: var(--accent);
    color: white;
}

.date-cell {
    font-size: 12px;
    color: var(--text-muted);
}

.actions-cell {
    text-align: center;
}

.admin-protected {
    color: var(--text-muted);
    font-size: 12px;
    font-style: italic;
}

.action-buttons-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: center;
}

.action-buttons-group .btn {
    min-width: 120px;
    justify-content: center;
}

/* Suspicious Login Warning */
.suspicious-warning {
    background: linear-gradient(135deg, rgba(255, 152, 0, 0.1), rgba(255, 152, 0, 0.05));
    border: 1px solid rgba(255, 152, 0, 0.3);
    border-radius: 4px;
    padding: 4px 8px;
    margin-top: 4px;
    font-size: 10px;
    color: #ff9800;
    font-weight: 500;
    animation: warningPulse 2s ease-in-out infinite;
}

@keyframes warningPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* IP History Modal */
.ip-history-modal {
    max-width: 600px;
    width: 100%;
}

.ip-history-modal .ip-history-header {
    margin-bottom: 20px;
    text-align: center;
}

.ip-history-modal .ip-history-header h3 {
    color: var(--accent);
    margin-bottom: 10px;
    font-size: 18px;
}

.ip-history-modal .ip-history-header p {
    color: var(--text-secondary);
    font-size: 14px;
}

.ip-history-list {
    max-height: 400px;
    overflow-y: auto;
    margin-bottom: 20px;
    padding: 10px;
    background: var(--bg-input);
    border-radius: var(--border-radius);
    border: 1px solid var(--border);
}

.ip-history-item {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: 15px;
    margin-bottom: 10px;
    border-left: 4px solid var(--border);
    transition: var(--transition);
}

.ip-history-item.recent {
    border-left-color: var(--success);
    background: linear-gradient(135deg, rgba(22, 198, 12, 0.1), rgba(22, 198, 12, 0.05));
}

.ip-history-item:hover {
    background: var(--bg-tertiary);
}

.ip-history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.ip-address {
    font-family: monospace;
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
}

.current-badge {
    background: var(--success);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
}

.ip-history-details {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.ip-history-details small {
    color: var(--text-secondary);
    font-size: 12px;
}

.ip-history-warning {
    background: linear-gradient(135deg, rgba(255, 152, 0, 0.1), rgba(255, 152, 0, 0.05));
    border: 1px solid rgba(255, 152, 0, 0.3);
    border-radius: var(--border-radius);
    padding: 15px;
    display: flex;
    gap: 12px;
    align-items: flex-start;
}

.warning-icon {
    font-size: 20px;
    flex-shrink: 0;
}

.warning-text {
    flex: 1;
    color: var(--text-primary);
    font-size: 14px;
    line-height: 1.5;
}

.warning-text strong {
    color: #ff9800;
}

.no-ip-history {
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
    padding: 40px 20px;
}

/* Info button styling */
.btn-info {
    background: linear-gradient(135deg, #2196f3, #1976d2);
    border-color: #2196f3;
    color: white;
}

.btn-info:hover:not(:disabled) {
    background: linear-gradient(135deg, #1976d2, #1565c0);
    border-color: #1976d2;
}

/* Loading and Error States */
.loading-cell,
.error-cell,
.empty-cell {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-secondary);
}

.loading-cell .loading-spinner {
    height: auto;
    margin: 0;
}

.error-cell {
    color: var(--error);
}

.empty-cell {
    color: var(--text-muted);
    font-style: italic;
}

/* Admin Tab Visibility */
.tab-button.admin-only {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 215, 0, 0.05));
    border: 1px solid rgba(255, 215, 0, 0.2);
}

.tab-button.admin-only:hover {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.15), rgba(255, 215, 0, 0.08));
    border-color: rgba(255, 215, 0, 0.3);
}

.tab-button.admin-only.active {
    background: var(--bg-primary);
    color: #ffd700;
    border-bottom: 2px solid #ffd700;
}

/* Responsive Admin Panel */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .users-table-container {
        overflow-x: auto;
    }
    
    .users-table {
        min-width: 600px;
    }
    
    .panel-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .admin-section {
        padding: 20px;
    }
    
    .admin-panel {
        padding: 20px;
    }
    
    .add-user-form {
        padding: 20px;
    }
    
    .users-table th,
    .users-table td {
        padding: 10px 15px;
    }
}

@media (max-width: 768px) {
    .app-header {
        padding: 15px 20px;
    }
    
    .header-content {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .tab-navigation {
        padding: 0 20px;
    }
    
    .main-content {
        padding: 20px;
    }
    
    .input-row {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .games-grid {
        grid-template-columns: 1fr;
    }
}

/* User Status Styles */
.user-status {
    font-size: 11px;
    margin-top: 2px;
}

.status-online {
    color: #4caf50;
    font-weight: 500;
}

.status-offline {
    color: #757575;
    font-weight: 400;
}

.last-login-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.last-login-info small {
    color: #888;
    font-size: 10px;
}

/* Security Notification Styles */
.security-notification-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 3000;
    backdrop-filter: blur(8px);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.security-notification-overlay.active {
    display: flex;
    opacity: 1;
}

.security-notification {
    background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
    border: 1px solid rgba(244, 67, 54, 0.3);
    border-radius: 16px;
    min-width: 400px;
    max-width: 500px;
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.5),
        0 0 0 1px rgba(244, 67, 54, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    animation: securityNotificationIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
    overflow: hidden;
    position: relative;
}

.security-notification::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #f44336, #ff5722, #f44336);
    background-size: 200% 100%;
    animation: securityGradient 2s ease-in-out infinite;
}

.security-notification-icon {
    text-align: center;
    padding: 30px 30px 20px 30px;
}

.security-icon-wrapper {
    width: 80px;
    height: 80px;
    margin: 0 auto;
    background: linear-gradient(135deg, rgba(244, 67, 54, 0.2), rgba(244, 67, 54, 0.1));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid rgba(244, 67, 54, 0.3);
    position: relative;
    animation: securityPulse 2s ease-in-out infinite;
}

.security-icon-wrapper::before {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border-radius: 50%;
    border: 2px solid rgba(244, 67, 54, 0.2);
    animation: securityRipple 2s ease-in-out infinite;
}

.security-icon {
    font-size: 32px;
    filter: drop-shadow(0 2px 8px rgba(244, 67, 54, 0.3));
}

.security-notification-content {
    padding: 0 30px 20px 30px;
    text-align: center;
}

.security-notification-title {
    font-size: 22px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 12px;
    background: linear-gradient(135deg, #f44336, #ff5722);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.security-notification-message {
    font-size: 16px;
    line-height: 1.6;
    color: var(--text-secondary);
    margin: 0;
}

.security-notification-actions {
    padding: 20px 30px 30px 30px;
    text-align: center;
}

.security-notification-btn {
    background: linear-gradient(135deg, #f44336, #d32f2f);
    border-color: #f44336;
    color: white;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 8px;
    box-shadow:
        0 4px 15px rgba(244, 67, 54, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.security-notification-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.security-notification-btn:hover {
    background: linear-gradient(135deg, #d32f2f, #c62828);
    border-color: #d32f2f;
    transform: translateY(-2px);
    box-shadow:
        0 6px 20px rgba(244, 67, 54, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.security-notification-btn:hover::before {
    left: 100%;
}

.security-notification-btn:active {
    transform: translateY(0);
    box-shadow:
        0 2px 8px rgba(244, 67, 54, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Animations */
@keyframes securityNotificationIn {
    0% {
        transform: scale(0.8) translateY(20px);
        opacity: 0;
    }
    100% {
        transform: scale(1) translateY(0);
        opacity: 1;
    }
}

/* Password Change Modal */
.password-change-modal {
    max-width: 400px;
    width: 100%;
}

.password-change-header {
    margin-bottom: 20px;
    text-align: center;
}

.password-change-header h3 {
    color: var(--accent);
    margin-bottom: 10px;
    font-size: 18px;
}

.password-change-header p {
    color: var(--text-secondary);
    font-size: 14px;
    margin: 0;
}

.password-change-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.password-change-form .form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.password-change-form .form-group label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
}

.password-change-form .form-group input {
    background: var(--bg-input);
    border: 1px solid var(--border);
    border-radius: var(--border-radius);
    padding: 12px 16px;
    color: var(--text-primary);
    font-size: 14px;
    transition: var(--transition);
    width: 100%;
}

.password-change-form .form-group input:focus {
    outline: none;
    border-color: var(--accent);
    box-shadow: 0 0 0 3px rgba(0, 120, 212, 0.1);
}

@keyframes securityPulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes securityRipple {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    100% {
        transform: scale(1.2);
        opacity: 0;
    }
}

@keyframes securityGradient {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

/* Responsive Security Notification */
@media (max-width: 480px) {
    .security-notification {
        min-width: 320px;
        margin: 20px;
    }
    
    .security-notification-icon {
        padding: 20px 20px 15px 20px;
    }
    
    .security-icon-wrapper {
        width: 60px;
        height: 60px;
    }
    
    .security-icon {
        font-size: 24px;
    }
    
    .security-notification-content {
        padding: 0 20px 15px 20px;
    }
    
    .security-notification-title {
        font-size: 18px;
    }
    
    .security-notification-message {
        font-size: 14px;
    }
    
    .security-notification-actions {
        padding: 15px 20px 20px 20px;
    }
}

/* IP History Link in Admin Table */
.ip-history-link {
    color: var(--accent);
    cursor: pointer;
    text-decoration: none;
    font-weight: 500;
    display: inline-block;
    transition: var(--transition);
}

.ip-history-link:hover {
    color: var(--accent-hover);
    text-decoration: underline;
    transform: translateY(-1px);
}