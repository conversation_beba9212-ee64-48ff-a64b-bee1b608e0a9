const { ipc<PERSON><PERSON><PERSON> } = require('electron');
const path = require('path');

// Global variables
let steamPath = null;
let selectedManifests = [];
let currentAppId = null;
let currentGameName = null;
let importFiles = [];
let currentDownload = null;

// DOM Elements
const elements = {
    // Export tab
    appIdInput: document.getElementById('app-id'),
    manifestModeRadios: document.querySelectorAll('input[name="manifest-mode"]'),
    scanBtn: document.getElementById('scan-btn'),
    createBtn: document.getElementById('create-btn'),
    openFolderBtn: document.getElementById('open-folder-btn'),
    manifestFolderInput: document.getElementById('manifest-folder'),
    configFileInput: document.getElementById('config-file'),
    outputFolderInput: document.getElementById('output-folder'),
    browseManifestBtn: document.getElementById('browse-manifest-btn'),
    browseConfigBtn: document.getElementById('browse-config-btn'),
    browseOutputBtn: document.getElementById('browse-output-btn'),
    logContainer: document.getElementById('log-container'),
    clearLogBtn: document.getElementById('clear-log-btn'),
    
    // Import tab
    dropZone: document.getElementById('drop-zone'),
    dropOverlay: document.getElementById('drop-overlay'),
    selectFilesBtn: document.getElementById('select-files-btn'),
    importFilesList: document.getElementById('import-files-list'),
    filesContainer: document.getElementById('files-container'),
    clearFilesBtn: document.getElementById('clear-files-btn'),
    importFilesBtn: document.getElementById('import-files-btn'),
    
    // Download tab
    downloadAppIdInput: document.getElementById('download-app-id'), // Keep for compatibility
    downloadGameSearchInput: document.getElementById('download-game-search'),
    downloadGameBtn: document.getElementById('download-game-btn'),
    downloadAndImportBtn: document.getElementById('download-and-import-btn'),
    downloadProgress: document.getElementById('download-progress'),
    progressStatus: document.getElementById('progress-status'),
    progressFill: document.getElementById('progress-fill'),
    progressText: document.getElementById('progress-text'),
    progressSize: document.getElementById('progress-size'),
    progressActions: document.getElementById('progress-actions'),
    progressImportBtn: document.getElementById('progress-import-btn'),
    downloadResults: document.getElementById('download-results'),
    downloadLogContainer: document.getElementById('download-log-container'),
    clearDownloadLogBtn: document.getElementById('clear-download-log-btn'),
    gamePreview: document.getElementById('game-preview'),
    gamePreviewImg: document.getElementById('game-preview-img'),
    gamePreviewName: document.getElementById('game-preview-name'),
    gamePreviewId: document.getElementById('game-preview-id'),
    
    // Header
    headerRestartSteamBtn: document.getElementById('header-restart-steam-btn'),
    
    // Games tab
    refreshGamesBtn: document.getElementById('refresh-games-btn'),
    gamesGrid: document.getElementById('games-grid'),
    gamesLoading: document.getElementById('games-loading'),
    
    // Tab navigation
    tabButtons: document.querySelectorAll('.tab-button'),
    tabContents: document.querySelectorAll('.tab-content'),
    
    // Modal
    modalOverlay: document.getElementById('modal-overlay'),
    modalTitle: document.getElementById('modal-title'),
    modalMessage: document.getElementById('modal-message'),
    modalClose: document.getElementById('modal-close'),
    modalCancel: document.getElementById('modal-cancel'),
    modalConfirm: document.getElementById('modal-confirm'),
    
    // Toast container
    toastContainer: document.getElementById('toast-container'),

    // IP History Modal
    ipHistoryModalOverlay: document.getElementById('ip-history-modal-overlay'),
    ipHistoryModalTitle: document.getElementById('ip-history-modal-title'),
    ipHistoryModalBody: document.getElementById('ip-history-modal-body'),
    ipHistoryClearBtn: document.getElementById('ip-history-clear-btn'),
    ipHistoryModalCancel: document.getElementById('ip-history-modal-cancel'),
    ipHistoryModalClose: document.getElementById('ip-history-modal-close'),
    
    // Folder status
    manifestFolderStatus: document.getElementById('manifest-folder-status'),
    configFileStatus: document.getElementById('config-file-status'),
    manualSteamPathContainer: document.getElementById('manual-steam-path-container'),
    manualSteamPathBtn: document.getElementById('manual-steam-path-btn')
  };

// Initialize app
async function initializeApp() {
    try {
        // Find Steam path
        steamPath = await ipcRenderer.invoke('find-steam-path');
        
        if (steamPath) {
            const depotcachePath = `${steamPath}\\depotcache`;
            const configPath = `${steamPath}\\config\\config.vdf`;
            elements.manifestFolderInput.value = depotcachePath;
            elements.configFileInput.value = configPath;
            
            logMessage('✅ Steam yolu bulundu: ' + steamPath, 'success');
        } else {
            logMessage('⚠️ Steam yolu bulunamadı! Lütfen manuel olarak seçin.', 'warning');
            elements.manualSteamPathContainer.style.display = 'block';
        }
        
        // Set default output folder to desktop
        const desktopPath = await ipcRenderer.invoke('get-desktop-path');
        elements.outputFolderInput.value = desktopPath;
        
        // Load installed games
        await refreshInstalledGames();
        
        // Update manifest folder status
        await updateManifestFolderStatus();
        
        // Update config file status
        await updateConfigFileStatus();
        
    } catch (error) {
        logMessage('❌ Başlatma hatası: ' + error.message, 'error');
    }
}

// Tab navigation
function initializeTabNavigation() {
    elements.tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const tabId = button.dataset.tab;
            switchTab(tabId);
        });
    });
}

function switchTab(tabId) {
    // Update tab buttons
    elements.tabButtons.forEach(btn => {
        btn.classList.toggle('active', btn.dataset.tab === tabId);
    });
    
    // Update tab contents
    elements.tabContents.forEach(content => {
        content.classList.toggle('active', content.id === `${tabId}-tab`);
    });
}

// Logging functions
function logMessage(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const icons = {
        success: '✅',
        error: '❌',
        warning: '⚠️',
        info: 'ℹ️'
    };
    
    const logElement = document.createElement('div');
    logElement.className = `log-message ${type}`;
    logElement.innerHTML = `
        <span class="log-time">[${timestamp}]</span>
        <span class="log-icon">${icons[type] || icons.info}</span>
        <span class="log-text">${message}</span>
    `;
    
    elements.logContainer.appendChild(logElement);
    elements.logContainer.scrollTop = elements.logContainer.scrollHeight;
}

function clearLog() {
    elements.logContainer.innerHTML = '';
    logMessage('Steam Delisi\'ne Hoş Geldiniz!', 'info');
    logMessage('Steam manifest dosyalarınızı hızlı ve kolay şekilde işleyin.', 'info');
}

// Toast notifications
function showToast(message, type = 'info', duration = 5000) {
    const icons = {
        success: '✅',
        error: '❌',
        warning: '⚠️',
        info: 'ℹ️'
    };
    
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.innerHTML = `
        <span class="toast-icon">${icons[type] || icons.info}</span>
        <span class="toast-message">${message}</span>
        <button class="toast-close">&times;</button>
    `;
    
    const closeBtn = toast.querySelector('.toast-close');
    closeBtn.addEventListener('click', () => {
        toast.remove();
    });
    
    elements.toastContainer.appendChild(toast);
    
    setTimeout(() => {
        if (toast.parentNode) {
            toast.remove();
        }
    }, duration);
}

// Modal functions
function showModal(title, message, confirmText = 'Onayla', cancelText = 'İptal') {
    return new Promise((resolve) => {
        elements.modalTitle.textContent = title;
        elements.modalMessage.textContent = message;
        elements.modalConfirm.textContent = confirmText;
        elements.modalCancel.textContent = cancelText;
        
        elements.modalOverlay.classList.add('active');
        
        const handleConfirm = () => {
            elements.modalOverlay.classList.remove('active');
            cleanup();
            resolve(true);
        };
        
        const handleCancel = () => {
            elements.modalOverlay.classList.remove('active');
            cleanup();
            resolve(false);
        };
        
        const cleanup = () => {
            elements.modalConfirm.removeEventListener('click', handleConfirm);
            elements.modalCancel.removeEventListener('click', handleCancel);
            elements.modalClose.removeEventListener('click', handleCancel);
        };
        
        elements.modalConfirm.addEventListener('click', handleConfirm);
        elements.modalCancel.addEventListener('click', handleCancel);
        elements.modalClose.addEventListener('click', handleCancel);
    });
}

// Update manifest folder status
async function updateManifestFolderStatus() {
    const manifestFolder = elements.manifestFolderInput.value.trim();
    const statusElement = elements.manifestFolderStatus;
    
    if (!manifestFolder) {
        statusElement.style.display = 'none';
        return;
    }
    
    // Show checking state
    statusElement.style.display = 'flex';
    statusElement.className = 'folder-status checking';
    statusElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Kontrol ediliyor...';
    
    try {
        const manifestCount = await ipcRenderer.invoke('count-manifests', manifestFolder);
        
        if (manifestCount > 0) {
            statusElement.className = 'folder-status success';
            statusElement.innerHTML = `<i class="fas fa-check"></i> ${manifestCount} manifest dosyası bulundu`;
        } else {
            statusElement.className = 'folder-status error';
            statusElement.innerHTML = '<i class="fas fa-times"></i> Manifest dosyası bulunamadı';
        }
    } catch (error) {
        statusElement.className = 'folder-status error';
        statusElement.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Klasör erişim hatası';
    }
}

// Update config file status
async function updateConfigFileStatus() {
    const configFile = elements.configFileInput.value.trim();
    const statusElement = elements.configFileStatus;
    
    if (!configFile) {
        statusElement.style.display = 'none';
        return;
    }
    
    // Show checking state
    statusElement.style.display = 'flex';
    statusElement.className = 'folder-status checking';
    statusElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Kontrol ediliyor...';
    
    try {
        const exists = await ipcRenderer.invoke('check-config-file', configFile);
        
        if (exists) {
            statusElement.className = 'folder-status success';
            statusElement.innerHTML = '<i class="fas fa-check"></i> Config.vdf dosyası bulundu';
        } else {
            statusElement.className = 'folder-status error';
            statusElement.innerHTML = '<i class="fas fa-times"></i> Config.vdf dosyası bulunamadı';
        }
    } catch (error) {
        statusElement.className = 'folder-status error';
        statusElement.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Dosya erişim hatası';
    }
}

// Export functions
async function scanManifests() {
    const appId = elements.appIdInput.value.trim();
    if (!appId) {
        showToast('Steam Oyun ID giriniz!', 'error');
        return;
    }
    
    const manifestFolder = elements.manifestFolderInput.value.trim();
    if (!manifestFolder) {
        showToast('Manifest klasörü seçiniz!', 'error');
        return;
    }
    
    const manifestMode = document.querySelector('input[name="manifest-mode"]:checked').value;
    
    try {
        logMessage(`🔍 App ID ${appId} için manifest dosyaları taranıyor...`, 'info');
        logMessage(`📁 Klasör: ${manifestFolder}`, 'info');
        
        // Get game name
        const gameName = await ipcRenderer.invoke('get-game-name', appId);
        if (gameName) {
            logMessage(`🎮 Oyun adı: ${gameName}`, 'success');
            currentGameName = gameName;
        }
        
        // Scan manifests with custom folder path
        selectedManifests = await ipcRenderer.invoke('scan-manifests', appId, manifestMode, manifestFolder);
        currentAppId = appId;
        
        logMessage(`✅ ${selectedManifests.length} manifest dosyası seçildi:`, 'success');
        selectedManifests.forEach(manifest => {
            logMessage(`  📄 ${manifest}`, 'info');
        });
        
        // Enable buttons
        elements.createBtn.disabled = false;
        
        showToast('Manifest tarama tamamlandı!', 'success');
        
    } catch (error) {
        logMessage(`❌ Tarama hatası: ${error.message}`, 'error');
        showToast('Manifest tarama başarısız!', 'error');
    }
}

async function createLua() {
    if (!selectedManifests.length) {
        showToast('Önce manifest tarama yapınız!', 'error');
        return;
    }
    
    const outputPath = elements.outputFolderInput.value.trim();
    if (!outputPath) {
        showToast('Çıktı klasörü seçiniz!', 'error');
        return;
    }
    
    try {
        logMessage('⚡ Lua dosyası oluşturuluyor...', 'info');
        
        const result = await ipcRenderer.invoke('create-lua', currentAppId, currentGameName, selectedManifests, outputPath);
        
        if (result.success) {
            logMessage(`✅ Lua dosyası oluşturuldu: ${result.outputPath}`, 'success');
            logMessage('🎉 İşlem başarıyla tamamlandı!', 'success');
            showToast('Lua dosyası başarıyla oluşturuldu!', 'success');
        }
        
    } catch (error) {
        logMessage(`❌ Lua oluşturma hatası: ${error.message}`, 'error');
        showToast('Lua oluşturma başarısız!', 'error');
    }
}

// Games functions
async function refreshInstalledGames() {
    try {
        elements.gamesLoading.style.display = 'flex';
        elements.gamesGrid.innerHTML = '';
        elements.gamesGrid.appendChild(elements.gamesLoading);
        
        const gameIds = await ipcRenderer.invoke('get-installed-games');
        
        elements.gamesLoading.style.display = 'none';
        
        if (gameIds.length === 0) {
            showEmptyGamesState();
            return;
        }
        
        // Create game cards
        for (const appId of gameIds) {
            await createGameCard(appId);
        }
        
    } catch (error) {
        elements.gamesLoading.style.display = 'none';
        logMessage(`❌ Oyun listesi yüklenirken hata: ${error.message}`, 'error');
        showToast('Oyun listesi yüklenemedi!', 'error');
    }
}

function showEmptyGamesState() {
    elements.gamesGrid.innerHTML = `
        <div class="empty-state">
            <div class="empty-state-icon">🎮</div>
            <h3>Henüz yüklü oyun bulunamadı</h3>
            <p>Steam'de yüklü oyunlar burada görünecektir. Önce bir oyun export edip Steam'e aktarın.</p>
        </div>
    `;
}

async function createGameCard(appId) {
    try {
        // Get game info
        const gameName = await ipcRenderer.invoke('get-game-name', appId) || `Bilinmeyen Oyun (ID: ${appId})`;
        const manifestCount = await ipcRenderer.invoke('get-manifest-count', appId);
        const gameImageUrl = await ipcRenderer.invoke('get-game-image', appId);
        
        // Truncate long names
        const displayName = gameName.length > 30 ? gameName.substring(0, 27) + '...' : gameName;
        
        const gameCard = document.createElement('div');
        gameCard.className = 'game-card';
        gameCard.innerHTML = `
            <div class="game-card-image">
                <img src="${gameImageUrl}" alt="${displayName}" class="game-cover"
                     onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                <div class="game-cover-fallback" style="display: none;">
                    <span class="game-icon">🎮</span>
                    <span class="game-title-fallback">${displayName}</span>
                </div>
                <span class="game-status">🎮 YÜKLÜ</span>
            </div>
            <div class="game-card-body">
                <div class="game-header">
                    <h3 class="game-title">${displayName}</h3>
                </div>
                <div class="game-info">
                    <div class="game-info-item">
                        <span class="info-icon">🆔</span>
                        <span class="info-text">Steam ID:</span>
                        <span class="info-value">${appId}</span>
                    </div>
                    <div class="game-info-item">
                        <span class="info-icon">📋</span>
                        <span class="info-text">Manifest:</span>
                        <span class="info-value">${manifestCount} dosya</span>
                    </div>
                    <div class="game-info-item">
                        <span class="info-icon">📁</span>
                        <span class="info-text">Lua:</span>
                        <span class="info-value">stplug-in/${appId}.lua</span>
                    </div>
                </div>
                <div class="game-actions">
                    <button class="btn btn-primary export-game-btn" data-app-id="${appId}" data-game-name="${gameName}">
                        <span class="btn-icon">📤</span>
                        Export
                    </button>
                    <button class="btn btn-danger delete-game-btn" data-app-id="${appId}" data-game-name="${gameName}">
                        <span class="btn-icon">🗑️</span>
                        Sil
                    </button>
                </div>
            </div>
        `;
        
        // Add event listeners
        const exportBtn = gameCard.querySelector('.export-game-btn');
        const deleteBtn = gameCard.querySelector('.delete-game-btn');
        
        exportBtn.addEventListener('click', () => exportGame(appId, gameName));
        deleteBtn.addEventListener('click', () => deleteGame(appId, gameName));
        
        elements.gamesGrid.appendChild(gameCard);
        
    } catch (error) {
        console.error('Error creating game card:', error);
    }
}

async function exportGame(appId, gameName) {
    try {
        const result = await ipcRenderer.invoke('export-game', appId, gameName);
        
        if (result.success) {
            showToast(`Oyun başarıyla export edildi! ${result.copiedFiles} dosya kopyalandı.`, 'success');
            logMessage(`✅ ${gameName} export edildi: ${result.exportPath}`, 'success');
        }
        
    } catch (error) {
        showToast('Export işlemi başarısız!', 'error');
        logMessage(`❌ Export hatası: ${error.message}`, 'error');
    }
}

async function deleteGame(appId, gameName) {
    const confirmed = await showModal(
        'Oyun Sil',
        `"${gameName}" oyununu silmek istediğinizden emin misiniz?\n\nBu işlem lua dosyasını ve tüm manifest dosyalarını silecektir.`,
        'Sil',
        'İptal'
    );
    
    if (!confirmed) return;
    
    try {
        const result = await ipcRenderer.invoke('delete-game', appId);
        
        if (result.success) {
            showToast(`Oyun başarıyla silindi! ${result.deletedFiles} dosya silindi.`, 'success');
            logMessage(`✅ ${gameName} silindi`, 'success');
            
            // Refresh games list
            await refreshInstalledGames();
        }
        
    } catch (error) {
        showToast('Silme işlemi başarısız!', 'error');
        logMessage(`❌ Silme hatası: ${error.message}`, 'error');
    }
}

async function restartSteam() {
    const confirmed = await showModal(
        'Steam\'i Yeniden Başlat',
        'Steam\'i yeniden başlatmak istediğinizden emin misiniz?\n\nBu işlem Steam\'i kapatıp tekrar açacaktır.',
        'Yeniden Başlat',
        'İptal'
    );
    
    if (!confirmed) return;
    
    try {
        showToast('Steam yeniden başlatılıyor...', 'info');
        logMessage('🔄 Steam yeniden başlatılıyor...', 'info');
        
        const result = await ipcRenderer.invoke('restart-steam');
        
        if (result.success) {
            showToast('Steam başarıyla yeniden başlatıldı!', 'success');
            logMessage('✅ Steam başarıyla yeniden başlatıldı!', 'success');
            logMessage('💡 Birkaç saniye bekleyip oyunları yenileyin.', 'warning');
        }
        
    } catch (error) {
        showToast('Steam yeniden başlatılamadı!', 'error');
        logMessage(`❌ Steam yeniden başlatma hatası: ${error.message}`, 'error');
    }
}

// Import functions
function initializeDragAndDrop() {
    const dropZone = elements.dropZone;
    const dropOverlay = elements.dropOverlay;
    let dragCounter = 0;
    
    // Prevent default drag behaviors
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropZone.addEventListener(eventName, preventDefaults, false);
        document.body.addEventListener(eventName, preventDefaults, false);
    });
    
    // Handle drag enter
    dropZone.addEventListener('dragenter', function(e) {
        dragCounter++;
        if (dragCounter === 1) {
            highlight();
        }
    }, false);
    
    // Handle drag over (just prevent default)
    dropZone.addEventListener('dragover', function(e) {
        // Sadece default davranışı engelle, highlight tetikleme
    }, false);
    
    // Handle drag leave
    dropZone.addEventListener('dragleave', function(e) {
        dragCounter--;
        if (dragCounter === 0) {
            unhighlight();
        }
    }, false);
    
    // Handle drop
    dropZone.addEventListener('drop', function(e) {
        dragCounter = 0;
        unhighlight();
        handleDrop(e);
    }, false);
    
    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }
    
    function highlight() {
        dropZone.classList.add('drag-over');
        dropOverlay.classList.add('active');
    }
    
    function unhighlight() {
        dropZone.classList.remove('drag-over');
        dropOverlay.classList.remove('active');
    }
    
    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        handleFiles(files);
    }
}

async function handleFiles(fileList) {
    const files = Array.from(fileList);
    const validFiles = files.filter(file => {
        const ext = file.name.toLowerCase().split('.').pop();
        return ext === 'lua' || ext === 'manifest';
    });
    
    if (validFiles.length === 0) {
        showToast('Geçerli dosya bulunamadı! Sadece .lua ve .manifest dosyaları desteklenir.', 'warning');
        return;
    }
    
    // Convert to file objects
    const fileObjects = validFiles.map(file => ({
        path: file.path,
        name: file.name,
        size: file.size,
        type: file.name.toLowerCase().split('.').pop()
    }));
    
    // Analyze files to get game information
    try {
        showToast('Dosyalar analiz ediliyor...', 'info');
        const gameInfo = await ipcRenderer.invoke('analyze-import-files', fileObjects);
        
        if (gameInfo.length > 0) {
            showGamePreview(gameInfo);
        }
        
        // Add files to import list
        fileObjects.forEach(fileObj => {
            const exists = importFiles.some(f => f.path === fileObj.path);
            if (!exists) {
                importFiles.push(fileObj);
            }
        });
        
        updateFilesList();
        showToast(`${validFiles.length} dosya eklendi!`, 'success');
        
    } catch (error) {
        console.error('File analysis error:', error);
        // Still add files even if analysis fails
        fileObjects.forEach(fileObj => {
            const exists = importFiles.some(f => f.path === fileObj.path);
            if (!exists) {
                importFiles.push(fileObj);
            }
        });
        
        updateFilesList();
        showToast(`${validFiles.length} dosya eklendi!`, 'success');
    }
}

async function selectFiles() {
    try {
        const files = await ipcRenderer.invoke('select-import-files');
        if (files && files.length > 0) {
            // Analyze files to get game information
            try {
                showToast('Dosyalar analiz ediliyor...', 'info');
                const gameInfo = await ipcRenderer.invoke('analyze-import-files', files);
                
                if (gameInfo.length > 0) {
                    showGamePreview(gameInfo);
                }
            } catch (error) {
                console.error('File analysis error:', error);
            }
            
            // Add files to import list
            files.forEach(file => {
                const exists = importFiles.some(f => f.path === file.path);
                if (!exists) {
                    importFiles.push(file);
                }
            });
            updateFilesList();
            showToast(`${files.length} dosya seçildi!`, 'success');
        }
    } catch (error) {
        showToast('Dosya seçimi başarısız!', 'error');
    }
}

function updateFilesList() {
    if (importFiles.length === 0) {
        elements.importFilesList.style.display = 'none';
        return;
    }
    
    elements.importFilesList.style.display = 'block';
    elements.filesContainer.innerHTML = '';
    
    importFiles.forEach((file, index) => {
        const fileItem = document.createElement('div');
        fileItem.className = 'file-item';
        
        const fileIcon = file.type === 'lua' ? '📄' : '📋';
        const fileSize = formatFileSize(file.size);
        
        fileItem.innerHTML = `
            <div class="file-info">
                <div class="file-icon">${fileIcon}</div>
                <div class="file-details">
                    <h4>${file.name}</h4>
                    <p>${fileSize} • ${file.type.toUpperCase()} dosyası</p>
                </div>
            </div>
            <div class="file-type ${file.type}">${file.type}</div>
            <button class="file-remove" onclick="removeFile(${index})">&times;</button>
        `;
        
        elements.filesContainer.appendChild(fileItem);
    });
}

function removeFile(index) {
    importFiles.splice(index, 1);
    updateFilesList();
    showToast('Dosya kaldırıldı', 'info');
}

function clearFiles() {
    importFiles = [];
    updateFilesList();
    showToast('Tüm dosyalar temizlendi', 'info');
}

async function importFilesToSteam() {
    if (importFiles.length === 0) {
        showToast('İçe aktarılacak dosya seçiniz!', 'warning');
        return;
    }
    
    const confirmed = await showModal(
        'Steam\'e İçe Aktar',
        `${importFiles.length} dosyayı Steam'e aktarmak istediğinizden emin misiniz?\n\nMevcut dosyalar üzerine yazılabilir.`,
        'İçe Aktar',
        'İptal'
    );
    
    if (!confirmed) return;
    
    try {
        showToast('Dosyalar Steam\'e aktarılıyor...', 'info');
        logMessage('📥 Dosyalar Steam\'e aktarılıyor...', 'info');
        
        const result = await ipcRenderer.invoke('import-files-to-steam', importFiles);
        
        if (result.success) {
            showToast(`${result.copiedFiles} dosya başarıyla aktarıldı!`, 'success');
            logMessage(`✅ ${result.copiedFiles} dosya Steam'e aktarıldı`, 'success');
            logMessage(`📁 Lua dosyaları: ${result.stplugPath}`, 'info');
            logMessage(`📋 Manifest dosyaları: ${result.depotcachePath}`, 'info');
            
            if (result.processedGames.length > 0) {
                logMessage(`🎮 İşlenen oyunlar: ${result.processedGames.join(', ')}`, 'info');
            }
            
            // Verify manifest files were actually copied
            if (result.manifestFiles && result.manifestFiles.length > 0) {
                logMessage('🔍 Manifest dosyaları doğrulanıyor...', 'info');
                
                try {
                    const verificationResults = await ipcRenderer.invoke('verify-imported-manifests', result.manifestFiles);
                    
                    let mainVerifiedCount = 0;
                    let configVerifiedCount = 0;
                    let totalFailedCount = 0;
                    
                    verificationResults.forEach(verification => {
                        const fileName = verification.file;
                        
                        // Check main depotcache
                        if (verification.mainDepotcache.exists) {
                            mainVerifiedCount++;
                            logMessage(`✅ ${fileName} ana depotcache'de doğrulandı (${formatFileSize(verification.mainDepotcache.size)})`, 'success');
                        } else {
                            logMessage(`❌ ${fileName} ana depotcache'de bulunamadı!`, 'error');
                            totalFailedCount++;
                        }
                        
                        // Check config depotcache
                        if (verification.configDepotcache.exists) {
                            configVerifiedCount++;
                            logMessage(`✅ ${fileName} config depotcache'de doğrulandı (${formatFileSize(verification.configDepotcache.size)})`, 'success');
                        } else {
                            logMessage(`❌ ${fileName} config depotcache'de bulunamadı!`, 'error');
                            totalFailedCount++;
                        }
                    });
                    
                    logMessage(`📊 Doğrulama Özeti:`, 'info');
                    logMessage(`  📁 Ana depotcache: ${mainVerifiedCount}/${result.manifestFiles.length} dosya`, mainVerifiedCount === result.manifestFiles.length ? 'success' : 'warning');
                    logMessage(`  📁 Config depotcache: ${configVerifiedCount}/${result.manifestFiles.length} dosya`, configVerifiedCount === result.manifestFiles.length ? 'success' : 'warning');
                    
                    if (totalFailedCount > 0) {
                        showToast(`⚠️ Bazı manifest dosyaları eksik!`, 'warning');
                        logMessage('💡 Eksik dosyalar Steam tarafından tanınmayabilir.', 'warning');
                    } else {
                        logMessage(`✅ Tüm manifest dosyaları her iki konuma da başarıyla kopyalandı`, 'success');
                    }
                    
                } catch (verifyError) {
                    logMessage(`⚠️ Manifest doğrulama hatası: ${verifyError.message}`, 'warning');
                }
            }
            
            logMessage('💡 Steam\'i yeniden başlatmanız önerilir.', 'warning');
            
            // Clear files after successful import
            clearFiles();
            
            // Refresh games list
            await refreshInstalledGames();
        }
        
    } catch (error) {
        showToast('İçe aktarma başarısız!', 'error');
        logMessage(`❌ İçe aktarma hatası: ${error.message}`, 'error');
    }
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function showGamePreview(gameInfo) {
    if (gameInfo.length === 0) return;
    
    // Create detailed game list for modal
    let gamesList = '';
    let totalGames = 0;
    
    gameInfo.forEach(game => {
        if (game.fileType === 'lua') {
            totalGames++;
            const manifestStatus = game.manifestCount > 0 ? `✅ ${game.manifestCount} Manifest` : '❌ Manifest yok';
            const statusIcon = game.manifestCount > 0 ? '✅' : '⚠️';
            
            gamesList += `
                <div class="game-preview-item ${game.manifestCount > 0 ? 'complete' : 'incomplete'}">
                    <div class="game-preview-header">
                        <span class="game-status-icon">${statusIcon}</span>
                        <h4 class="game-name">${game.gameName}</h4>
                    </div>
                    <div class="game-preview-details">
                        <span class="game-id">ID: ${game.appId}</span>
                        <span class="lua-status">✅ Lua</span>
                        <span class="manifest-status">${manifestStatus}</span>
                    </div>
                </div>
            `;
        }
    });
    
    // Show modal with game preview
    showGamePreviewModal(totalGames, gamesList);
    
    // Also log to console for detailed view
    logMessage('📋 Dosya Analizi Tamamlandı:', 'info');
    gameInfo.forEach(game => {
        if (game.fileType === 'lua') {
            const status = game.manifestCount > 0 ? '✅ Tam' : '⚠️ Eksik';
            logMessage(`${status} ${game.gameName} (ID: ${game.appId})`, game.manifestCount > 0 ? 'success' : 'warning');
            logMessage(`  📄 Lua dosyası mevcut`, 'info');
            if (game.manifestCount > 0) {
                logMessage(`  📋 ${game.manifestCount} manifest dosyası`, 'info');
            }
        }
    });
}

function showGamePreviewModal(totalGames, gamesList) {
    // Create modal content
    const modalContent = `
        <div class="game-preview-modal">
            <div class="game-preview-header">
                <h3>🎮 Yüklenecek Oyunlar (${totalGames})</h3>
                <p>Dosya analizi tamamlandı. Aşağıdaki oyunlar Steam'e yüklenecek:</p>
            </div>
            <div class="games-preview-list">
                ${gamesList}
            </div>
        </div>
    `;
    
    // Show modal
    elements.modalTitle.innerHTML = '🎮 Dosya Analizi Sonuçları';
    elements.modalMessage.innerHTML = modalContent;
    elements.modalConfirm.textContent = 'Steam\'e Aktar';
    elements.modalCancel.textContent = 'Devam Et';
    elements.modalCancel.style.display = 'inline-flex';
    
    elements.modalOverlay.classList.add('active');
    
    const handleImport = async () => {
        elements.modalOverlay.classList.remove('active');
        cleanup();
        // Trigger import to Steam
        await importFilesToSteam();
    };
    
    const handleContinue = () => {
        elements.modalOverlay.classList.remove('active');
        cleanup();
    };
    
    const cleanup = () => {
        elements.modalConfirm.removeEventListener('click', handleImport);
        elements.modalCancel.removeEventListener('click', handleContinue);
        elements.modalClose.removeEventListener('click', handleContinue);
    };
    
    elements.modalConfirm.addEventListener('click', handleImport);
    elements.modalCancel.addEventListener('click', handleContinue);
    elements.modalClose.addEventListener('click', handleContinue);
}

// Make removeFile global for onclick
window.removeFile = removeFile;

// Config file selection
async function selectConfigFile() {
    try {
        const filePath = await ipcRenderer.invoke('select-config-file');
        if (filePath) {
            elements.configFileInput.value = filePath;
            await updateConfigFileStatus();
        }
    } catch (error) {
        showToast('Config dosyası seçimi başarısız!', 'error');
    }
}

// Folder selection
async function selectFolder(inputElement) {
    try {
        const folderPath = await ipcRenderer.invoke('select-folder');
        if (folderPath) {
            inputElement.value = folderPath;
        }
    } catch (error) {
        showToast('Klasör seçimi başarısız!', 'error');
    }
}

async function openOutputFolder() {
    const outputPath = elements.outputFolderInput.value.trim();
    if (outputPath) {
        await ipcRenderer.invoke('open-folder', outputPath);
    } else {
        showToast('Çıktı klasörü belirtilmemiş!', 'warning');
    }
}

async function selectSteamPathManually() {
    try {
        const selectedPath = await ipcRenderer.invoke('select-steam-path');
        if (selectedPath) {
            steamPath = selectedPath;
            const depotcachePath = `${steamPath}\\depotcache`;
            const configPath = `${steamPath}\\config\\config.vdf`;
            elements.manifestFolderInput.value = depotcachePath;
            elements.configFileInput.value = configPath;
            
            logMessage(`✅ Steam yolu manuel olarak ayarlandı: ${steamPath}`, 'success');
            showToast('Steam yolu başarıyla ayarlandı!', 'success');
            
            // Hide the manual selection button
            elements.manualSteamPathContainer.style.display = 'none';
            
            // Re-check folder statuses
            await updateManifestFolderStatus();
            await updateConfigFileStatus();
        }
    } catch (error) {
        showToast('Steam yolu seçimi başarısız!', 'error');
        logMessage(`❌ Steam yolu seçme hatası: ${error.message}`, 'error');
    }
}

// Download functions
async function downloadGame(autoImport = false) {
    // Get app ID from the smart search input
    let appId = '';
    const searchInput = document.getElementById('download-game-search');
    
    if (searchInput && searchInput.value.trim()) {
        const searchValue = searchInput.value.trim();
        
        // If it's a number, use it directly as app ID
        if (/^\d+$/.test(searchValue)) {
            appId = searchValue;
        } else {
            // If it's text, try to extract app ID from game preview
            const gamePreviewId = elements.gamePreviewId.textContent;
            const idMatch = gamePreviewId.match(/Steam ID:\s*(\d+)/);
            if (idMatch) {
                appId = idMatch[1];
            } else {
                showToast('Lütfen geçerli bir Steam Oyun ID girin veya listeden bir oyun seçin!', 'error');
                return;
            }
        }
    } else {
        // Fallback to old input if exists
        appId = elements.downloadAppIdInput ? elements.downloadAppIdInput.value.trim() : '';
    }
    
    if (!appId) {
        showToast('Steam Oyun ID giriniz!', 'error');
        return;
    }
    
    if (!/^\d+$/.test(appId)) {
        showToast('Geçerli bir Steam Oyun ID giriniz!', 'error');
        return;
    }
    
    try {
        // Disable buttons and show progress
        elements.downloadGameBtn.disabled = true;
        elements.downloadAndImportBtn.disabled = true;
        elements.downloadProgress.style.display = 'block';
        elements.downloadResults.style.display = 'block';
        elements.progressActions.style.display = 'none';
        
        // Reset progress
        updateDownloadProgress(0, 'Hazırlanıyor...', 0, 0);
        
        currentDownload = appId;
        
        downloadLogMessage(`🚀 Oyun indirme başlatıldı: ID ${appId}`, 'info');
        
        const result = await ipcRenderer.invoke('download-game', appId, autoImport);
        
        if (result.success) {
            downloadLogMessage(`✅ İndirme tamamlandı!`, 'success');
            downloadLogMessage(`📁 Çıkarılan dosyalar: ${result.extractedFiles.length}`, 'info');
            
            result.extractedFiles.forEach(file => {
                const icon = file.type === 'lua' ? '📄' : '📋';
                downloadLogMessage(`  ${icon} ${file.name} (${formatFileSize(file.size)})`, 'info');
            });
            
            if (autoImport && result.importResult) {
                downloadLogMessage(`🚀 Steam'e aktarım tamamlandı!`, 'success');
                downloadLogMessage(`📥 ${result.importResult.copiedFiles} dosya kopyalandı`, 'success');
                
                // Refresh games list
                await refreshInstalledGames();
            } else if (!autoImport) {
                // Show import button in progress section
                showProgressImportButton(appId, result.extractedFiles);
            }
            
            showToast(autoImport ? 'Oyun indirildi ve Steam\'e aktarıldı!' : 'Oyun başarıyla indirildi!', 'success');
        }
        
    } catch (error) {
        downloadLogMessage(`❌ İndirme hatası: ${error.message}`, 'error');
        showToast('Oyun indirme başarısız!', 'error');
        updateDownloadProgress(0, 'Hata oluştu', 0, 0);
    } finally {
        // Re-enable buttons
        elements.downloadGameBtn.disabled = false;
        elements.downloadAndImportBtn.disabled = false;
        currentDownload = null;
    }
}

function updateDownloadProgress(percent, status, downloaded = 0, total = 0) {
    elements.progressFill.style.width = `${percent}%`;
    elements.progressText.textContent = `${percent}%`;
    elements.progressStatus.textContent = status;
    
    if (total > 0) {
        const downloadedMB = (downloaded / (1024 * 1024)).toFixed(1);
        const totalMB = (total / (1024 * 1024)).toFixed(1);
        elements.progressSize.textContent = `${downloadedMB} / ${totalMB} MB`;
    } else {
        elements.progressSize.textContent = '';
    }
}

function downloadLogMessage(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const icons = {
        success: '✅',
        error: '❌',
        warning: '⚠️',
        info: 'ℹ️'
    };
    
    const logElement = document.createElement('div');
    logElement.className = `log-message ${type}`;
    logElement.innerHTML = `
        <span class="log-time">[${timestamp}]</span>
        <span class="log-icon">${icons[type] || icons.info}</span>
        <span class="log-text">${message}</span>
    `;
    
    elements.downloadLogContainer.appendChild(logElement);
    elements.downloadLogContainer.scrollTop = elements.downloadLogContainer.scrollHeight;
}

function clearDownloadLog() {
    elements.downloadLogContainer.innerHTML = '';
    downloadLogMessage('Oyun indirme hazır!', 'info');
}

// Listen for download progress updates from main process
ipcRenderer.on('download-progress', (event, data) => {
    if (data.appId === currentDownload) {
        let statusText = '';
        
        switch (data.status) {
            case 'downloading':
                statusText = 'İndiriliyor...';
                break;
            case 'extracting':
                statusText = 'Çıkarılıyor...';
                break;
            case 'importing':
                statusText = 'Steam\'e aktarılıyor...';
                break;
            case 'completed':
                statusText = 'Tamamlandı!';
                break;
            case 'error':
                statusText = 'Hata oluştu!';
                downloadLogMessage(`❌ ${data.error}`, 'error');
                break;
        }
        
        updateDownloadProgress(data.progress, statusText, data.downloaded, data.total);
    }
});

function showProgressImportButton(appId, extractedFiles) {
    // Show the import button in progress section
    elements.progressActions.style.display = 'block';
    
    // Store the data for import
    elements.progressImportBtn.dataset.appId = appId;
    elements.progressImportBtn.dataset.extractedFiles = JSON.stringify(extractedFiles);
    
    // Reset button state
    elements.progressImportBtn.disabled = false;
    elements.progressImportBtn.innerHTML = '<span class="btn-icon">🚀</span> Steam\'e Aktar';
}

async function importDownloadedGameFromProgress() {
    try {
        const appId = elements.progressImportBtn.dataset.appId;
        const extractedFiles = JSON.parse(elements.progressImportBtn.dataset.extractedFiles);
        
        elements.progressImportBtn.disabled = true;
        elements.progressImportBtn.innerHTML = '<span class="btn-icon">⏳</span> Aktarılıyor...';
        
        downloadLogMessage(`🚀 Steam'e aktarım başlatıldı...`, 'info');
        
        // Import the extracted files
        const importResult = await ipcRenderer.invoke('import-files-to-steam', extractedFiles);
        
        if (importResult.success) {
            downloadLogMessage(`✅ Steam'e aktarım tamamlandı!`, 'success');
            downloadLogMessage(`📥 ${importResult.copiedFiles} dosya kopyalandı`, 'success');
            
            // Hide the import button
            elements.progressActions.style.display = 'none';
            
            // Refresh games list
            await refreshInstalledGames();
            
            showToast('Oyun başarıyla Steam\'e aktarıldı!', 'success');
        }
        
    } catch (error) {
        downloadLogMessage(`❌ Aktarım hatası: ${error.message}`, 'error');
        showToast('Steam\'e aktarım başarısız!', 'error');
        
        // Re-enable button
        elements.progressImportBtn.disabled = false;
        elements.progressImportBtn.innerHTML = '<span class="btn-icon">🚀</span> Steam\'e Aktar';
    }
}


// Make removeFile global for onclick
window.removeFile = removeFile;

// Event listeners
function initializeEventListeners() {
    // Export tab events
    elements.scanBtn.addEventListener('click', scanManifests);
    elements.createBtn.addEventListener('click', createLua);
    elements.openFolderBtn.addEventListener('click', openOutputFolder);
    elements.clearLogBtn.addEventListener('click', clearLog);
    
    elements.browseManifestBtn.addEventListener('click', () => {
        selectFolder(elements.manifestFolderInput);
    });
    
    elements.browseOutputBtn.addEventListener('click', () => {
        selectFolder(elements.outputFolderInput);
    });
    
    // Manifest folder input change event
    elements.manifestFolderInput.addEventListener('input', updateManifestFolderStatus);
    elements.manifestFolderInput.addEventListener('change', updateManifestFolderStatus);
    
    // Config file input change event
    elements.configFileInput.addEventListener('input', updateConfigFileStatus);
    elements.configFileInput.addEventListener('change', updateConfigFileStatus);
    
    elements.browseConfigBtn.addEventListener('click', () => {
        selectConfigFile();
    });

    elements.manualSteamPathBtn.addEventListener('click', selectSteamPathManually);
    
    // Import tab events
    elements.selectFilesBtn.addEventListener('click', selectFiles);
    elements.clearFilesBtn.addEventListener('click', clearFiles);
    elements.importFilesBtn.addEventListener('click', importFilesToSteam);
    
    // Download tab events
    elements.downloadGameBtn.addEventListener('click', () => downloadGame(false));
    elements.downloadAndImportBtn.addEventListener('click', () => downloadGame(true));
    elements.clearDownloadLogBtn.addEventListener('click', clearDownloadLog);
    elements.progressImportBtn.addEventListener('click', importDownloadedGameFromProgress);
    
    // Smart search input (ID or name)
    const gameSearchInput = document.getElementById('download-game-search');
    if (gameSearchInput) {
        gameSearchInput.addEventListener('input', handleSmartGameSearch);
        gameSearchInput.addEventListener('keydown', handleAutocompleteKeydown);
        gameSearchInput.addEventListener('blur', hideAutocomplete);
        gameSearchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                // If autocomplete is visible and item is selected, use that
                if (selectedAutocompleteIndex >= 0 && autocompleteResults.length > 0) {
                    const dropdown = document.getElementById('autocomplete-dropdown');
                    const items = dropdown.querySelectorAll('.autocomplete-item');
                    if (items[selectedAutocompleteIndex]) {
                        selectAutocompleteItem(items[selectedAutocompleteIndex]);
                        return;
                    }
                }
                // Otherwise start download
                downloadGame(false);
            }
        });
    }
    
    // Keep old input for compatibility (hidden now)
    if (elements.downloadAppIdInput) {
        elements.downloadAppIdInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                downloadGame(false);
            }
        });
    }
    
    // Header events
    elements.headerRestartSteamBtn.addEventListener('click', restartSteam);
    
    // Games tab events
    elements.refreshGamesBtn.addEventListener('click', refreshInstalledGames);
    
    // Enter key support for app ID input
    elements.appIdInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            scanManifests();
        }
    });
    
    // Modal close events
    elements.modalOverlay.addEventListener('click', (e) => {
        if (e.target === elements.modalOverlay) {
            elements.modalOverlay.classList.remove('active');
        }
    });
}


// Admin Panel Functions
let currentUser = null;

// Initialize admin panel
function initializeAdminPanel() {
    // Get current user from localStorage
    const userStr = localStorage.getItem('currentUser');
    if (userStr) {
        currentUser = JSON.parse(userStr);
        
        // Only initialize admin panel if user is admin
        if (currentUser.role === 'admin') {
            console.log('Initializing admin panel for admin user:', currentUser.username);
            
            // Wait a bit more to ensure DOM is fully loaded
            setTimeout(() => {
                console.log('Setting up admin panel event listeners...');
                initializeAdminEventListeners();
                loadUsers();
            }, 500);
        } else {
            console.log('User is not admin, skipping admin panel initialization');
        }
    } else {
        console.log('No current user found, skipping admin panel initialization');
    }
}

function initializeAdminEventListeners() {
    // Add user form
    const addUserForm = document.getElementById('add-user-form');
    if (addUserForm) {
        console.log('Adding event listener to add-user-form');
        
        // Remove any existing event listeners first
        addUserForm.removeEventListener('submit', handleAddUser);
        addUserForm.addEventListener('submit', handleAddUser);
        
        // Also add click listener to submit button as backup
        const submitBtn = addUserForm.querySelector('button[type="submit"]');
        if (submitBtn) {
            console.log('Adding click listener to submit button');
            submitBtn.removeEventListener('click', handleAddUserClick);
            submitBtn.addEventListener('click', handleAddUserClick);
        }
    } else {
        console.error('add-user-form element not found!');
    }
    
    // Refresh users button
    const refreshUsersBtn = document.getElementById('refresh-users-btn');
    if (refreshUsersBtn) {
        console.log('Adding event listener to refresh-users-btn');
        refreshUsersBtn.addEventListener('click', loadUsers);
    } else {
        console.error('refresh-users-btn element not found!');
    }

    // Save settings button
    const saveSettingsBtn = document.getElementById('save-settings-btn');
    if (saveSettingsBtn) {
        console.log('Adding event listener to save-settings-btn');
        saveSettingsBtn.addEventListener('click', handleSaveSettings);
    } else {
        console.error('save-settings-btn element not found!');
    }
}

// Backup click handler for submit button
async function handleAddUserClick(e) {
    console.log('Submit button clicked');
    e.preventDefault();
    
    // Find the form and trigger submit
    const form = e.target.closest('form');
    if (form) {
        console.log('Form found, calling handleAddUser directly');
        await handleAddUser(e);
    }
}

async function handleAddUser(e) {
    console.log('=== HANDLE ADD USER START ===');
    e.preventDefault();
    e.stopPropagation();
    
    // Simple and direct approach
    const form = document.getElementById('add-user-form');
    if (!form) {
        console.error('Form not found!');
        showToast('Form bulunamadı!', 'error');
        return;
    }
    
    console.log('Form found:', form);
    
    // Get form data using FormData API
    const formData = new FormData(form);
    const username = formData.get('username') || document.getElementById('new-username')?.value?.trim() || '';
    const email = formData.get('email') || document.getElementById('new-email')?.value?.trim() || '';
    const password = formData.get('password') || document.getElementById('new-password')?.value || '';
    
    console.log('=== FORM DATA EXTRACTED ===');
    console.log('Username:', username || '(empty)');
    console.log('Email:', email || '(empty)');
    console.log('Password length:', password.length);
    console.log('Password exists:', !!password);
    
    // Direct element access as backup
    const usernameEl = document.getElementById('new-username');
    const emailEl = document.getElementById('new-email');
    const passwordEl = document.getElementById('new-password');
    
    console.log('=== DIRECT ELEMENT ACCESS ===');
    console.log('Username element value:', usernameEl?.value || '(not found)');
    console.log('Email element value:', emailEl?.value || '(not found)');
    console.log('Password element value length:', passwordEl?.value?.length || 0);
    console.log('Password element type:', passwordEl?.type || '(not found)');
    
    // Use direct values if FormData failed
    const finalUsername = username || usernameEl?.value?.trim() || '';
    const finalEmail = email || emailEl?.value?.trim() || '';
    const finalPassword = password || passwordEl?.value || '';
    
    console.log('=== FINAL VALUES ===');
    console.log('Final username:', finalUsername || '(empty)');
    console.log('Final email:', finalEmail || '(empty)');
    console.log('Final password length:', finalPassword.length);
    
    // Validation
    if (!finalUsername) {
        console.log('Username validation failed');
        showToast('Kullanıcı adı boş olamaz!', 'error');
        usernameEl?.focus();
        return;
    }
    
    if (!finalEmail) {
        console.log('Email validation failed');
        showToast('E-posta adresi boş olamaz!', 'error');
        emailEl?.focus();
        return;
    }
    
    if (!finalPassword || finalPassword.length === 0) {
        console.log('Password validation failed - empty password');
        console.log('Password element:', passwordEl);
        console.log('Password element value:', passwordEl?.value);
        console.log('Password element attributes:', {
            id: passwordEl?.id,
            type: passwordEl?.type,
            name: passwordEl?.name,
            required: passwordEl?.required
        });
        
        showToast('Şifre boş olamaz!', 'error');
        passwordEl?.focus();
        return;
    }
    
    if (finalPassword.length < 6) {
        console.log('Password length validation failed:', finalPassword.length);
        showToast('Şifre en az 6 karakter olmalıdır!', 'error');
        passwordEl?.focus();
        return;
    }
    
    // Email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(finalEmail)) {
        console.log('Email format validation failed');
        showToast('Geçerli bir e-posta adresi girin!', 'error');
        emailEl?.focus();
        return;
    }
    
    try {
        console.log('=== SENDING TO MAIN PROCESS ===');
        showToast('Kullanıcı oluşturuluyor...', 'info');
        
        const result = await ipcRenderer.invoke('admin-create-user', {
            username: finalUsername,
            email: finalEmail,
            password: finalPassword
        });
        
        console.log('=== CREATE USER RESULT ===');
        console.log('Result:', result);
        
        if (result.success) {
            showToast('Kullanıcı başarıyla oluşturuldu!', 'success');
            
            // Clear form
            if (usernameEl) usernameEl.value = '';
            if (emailEl) emailEl.value = '';
            if (passwordEl) passwordEl.value = '';
            
            console.log('Form cleared, reloading users...');
            
            // Reload users list
            await loadUsers();
        } else {
            console.error('Create user failed:', result.error);
            showToast(result.error || 'Kullanıcı oluşturulamadı!', 'error');
        }
        
    } catch (error) {
        console.error('Create user error:', error);
        showToast('Bir hata oluştu: ' + error.message, 'error');
    }
    
    console.log('=== HANDLE ADD USER END ===');
}

async function loadUsers() {
    try {
        const usersTableBody = document.getElementById('users-table-body');
        if (!usersTableBody) return;
        
        // Show loading
        usersTableBody.innerHTML = `
            <tr>
                <td colspan="6" class="loading-cell">
                    <div class="loading-spinner">
                        <div class="spinner"></div>
                        Kullanıcılar yükleniyor...
                    </div>
                </td>
            </tr>
        `;
        
        const result = await ipcRenderer.invoke('admin-get-users');
        
        if (result.success) {
            displayUsers(result.users);
        } else {
            usersTableBody.innerHTML = `
                <tr>
                    <td colspan="6" class="error-cell">
                        ❌ Kullanıcılar yüklenemedi: ${result.error}
                    </td>
                </tr>
            `;
        }
        
    } catch (error) {
        console.error('Load users error:', error);
        const usersTableBody = document.getElementById('users-table-body');
        if (usersTableBody) {
            usersTableBody.innerHTML = `
                <tr>
                    <td colspan="6" class="error-cell">
                        ❌ Bir hata oluştu!
                    </td>
                </tr>
            `;
        }
    }
}

function displayUsers(users) {
    const usersTableBody = document.getElementById('users-table-body');
    if (!usersTableBody) return;
    
    if (users.length === 0) {
        usersTableBody.innerHTML = `
            <tr>
                <td colspan="6" class="empty-cell">
                    👤 Henüz kullanıcı bulunmuyor
                </td>
            </tr>
        `;
        return;
    }
    
    usersTableBody.innerHTML = '';
    
    users.forEach(user => {
        const row = document.createElement('tr');
        
        const createdDate = user.createdAt ? new Date(user.createdAt).toLocaleDateString('tr-TR') : 'Bilinmiyor';
        const lastLoginDate = user.lastLogin ? new Date(user.lastLogin).toLocaleDateString('tr-TR') + ' ' + new Date(user.lastLogin).toLocaleTimeString('tr-TR') : 'Hiç giriş yapmamış';
        
        const roleClass = user.role === 'admin' ? 'role-admin' : 'role-user';
        const roleBadge = user.role === 'admin' ? '👑 Admin' : '👤 Kullanıcı';
        
        // Check if user is currently online (last login within 5 minutes)
        const isOnline = user.lastLogin && (Date.now() - new Date(user.lastLogin).getTime()) < 5 * 60 * 1000;
        const onlineStatus = isOnline ? '<span class="status-online">🟢 Çevrimiçi</span>' : '<span class="status-offline">⚫ Çevrimdışı</span>';
        
        // Device and platform info
        const deviceInfo = user.loginDevice || 'Bilinmiyor';
        const platformInfo = user.loginPlatform || 'Bilinmiyor';
        const ipInfo = user.loginIP || 'Bilinmiyor';
        
        // IP History and suspicious login detection
        const ipHistory = user.ipHistory || [];
        const ipCount = ipHistory.length;
        const suspiciousLogin = user.suspiciousLogin || false;
        
        // Create IP history display
        let ipHistoryDisplay = '';
        if (ipCount > 0) {
            const latestIp = ipHistory.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))[0].ip;
            ipHistoryDisplay = `
                <div class="ip-history-link" data-user-id="${user.id}" data-username="${user.username}" data-ip-history='${JSON.stringify(ipHistory)}'>
                    <small>🌐 ${latestIp} (${ipCount} farklı IP)</small>
                </div>
            `;
        } else {
            ipHistoryDisplay = `<small>🌐 ${ipInfo}</small>`;
        }
        
        // Suspicious login warning
        const suspiciousWarning = suspiciousLogin ?
            '<div class="suspicious-warning">⚠️ Farklı IP\'den giriş tespit edildi!</div>' : '';
        
        row.innerHTML = `
            <td class="username-cell">
                <strong>${user.username}</strong>
                <div class="user-status">${onlineStatus}</div>
                ${suspiciousWarning}
            </td>
            <td class="email-cell">
                ${user.email}
            </td>
            <td class="role-cell">
                <span class="role-badge ${roleClass}">${roleBadge}</span>
            </td>
            <td class="date-cell">
                ${createdDate}
            </td>
            <td class="date-cell">
                <div class="last-login-info">
                    <div>${lastLoginDate}</div>
                    <small>💻 ${deviceInfo}</small>
                    <small>🖥️ ${platformInfo}</small>
                    ${ipHistoryDisplay}
                </div>
            </td>
            <td class="actions-cell">
                ${user.role !== 'admin' ? `
                    <div class="action-buttons-group">
                        <button class="btn btn-small btn-warning change-password-btn"
                                data-user-id="${user.id}" data-username="${user.username}">
                            <span class="btn-icon">🔑</span>
                            Şifre Değiştir
                        </button>
                        <button class="btn btn-small btn-danger delete-user-btn"
                                data-user-id="${user.id}" data-username="${user.username}" data-email="${user.email}">
                            <span class="btn-icon">🗑️</span>
                            Sil
                        </button>
                    </div>
                ` : '<span class="admin-protected">🔒 Korumalı</span>'}
            </td>
        `;
        
        usersTableBody.appendChild(row);
    });
    
    // Add event listeners for action buttons
    const changePasswordBtns = usersTableBody.querySelectorAll('.change-password-btn');
    changePasswordBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            const userId = btn.dataset.userId;
            const username = btn.dataset.username;
            handleAdminChangePassword(userId, username);
        });
    });
    
    const deleteUserBtns = usersTableBody.querySelectorAll('.delete-user-btn');
    deleteUserBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            const userId = btn.dataset.userId;
            const username = btn.dataset.username;
            const email = btn.dataset.email;
            handleDeleteUser(userId, username, email);
        });
    });
    
    const viewIpHistoryLinks = usersTableBody.querySelectorAll('.ip-history-link');
    viewIpHistoryLinks.forEach(link => {
        link.addEventListener('click', () => {
            const userId = link.dataset.userId;
            const username = link.dataset.username;
            const ipHistory = JSON.parse(link.dataset.ipHistory);
            showIpHistoryModal(userId, username, ipHistory);
        });
    });
}

async function handleAdminChangePassword(userId, username) {
    // Create a custom modal for password input
    showPasswordChangeModal(userId, username);
}

function showPasswordChangeModal(userId, username) {
    const modalContent = `
        <div class="password-change-modal">
            <div class="password-change-header">
                <h3>🔑 Şifre Değiştir</h3>
                <p><strong>${username}</strong> kullanıcısı için yeni şifre belirleyin:</p>
            </div>
            <div class="password-change-form">
                <div class="form-group">
                    <label for="admin-new-password">Yeni Şifre:</label>
                    <input type="password" id="admin-new-password" placeholder="Yeni şifre (min. 6 karakter)" minlength="6" required>
                </div>
                <div class="form-group">
                    <label for="admin-confirm-password">Şifre Tekrar:</label>
                    <input type="password" id="admin-confirm-password" placeholder="Şifreyi tekrar girin" minlength="6" required>
                </div>
            </div>
        </div>
    `;
    
    // Show modal
    elements.modalTitle.innerHTML = '🔑 Şifre Değiştir';
    elements.modalMessage.innerHTML = modalContent;
    elements.modalConfirm.textContent = 'Şifreyi Değiştir';
    elements.modalCancel.textContent = 'İptal';
    elements.modalCancel.style.display = 'inline-flex';
    
    elements.modalOverlay.classList.add('active');
    
    // Focus on first input
    setTimeout(() => {
        const passwordInput = document.getElementById('admin-new-password');
        if (passwordInput) passwordInput.focus();
    }, 100);
    
    const handlePasswordChange = async () => {
        const newPassword = document.getElementById('admin-new-password')?.value || '';
        const confirmPassword = document.getElementById('admin-confirm-password')?.value || '';
        
        // Validation
        if (!newPassword) {
            showToast('Yeni şifre boş olamaz!', 'error');
            return;
        }
        
        if (newPassword.length < 6) {
            showToast('Şifre en az 6 karakter olmalıdır!', 'error');
            return;
        }
        
        if (newPassword !== confirmPassword) {
            showToast('Şifreler eşleşmiyor!', 'error');
            return;
        }
        
        elements.modalOverlay.classList.remove('active');
        cleanup();
        
        try {
            showToast('Şifre değiştiriliyor...', 'info');
            
            const result = await ipcRenderer.invoke('admin-update-user-password', {
                userId,
                newPassword
            });
            
            if (result.success) {
                showToast('Şifre başarıyla değiştirildi!', 'success');
            } else {
                showToast(result.error || 'Şifre değiştirilemedi!', 'error');
            }
            
        } catch (error) {
            console.error('Change password error:', error);
            showToast('Bir hata oluştu!', 'error');
        }
    };
    
    const handleCancel = () => {
        elements.modalOverlay.classList.remove('active');
        cleanup();
    };
    
    const cleanup = () => {
        elements.modalConfirm.removeEventListener('click', handlePasswordChange);
        elements.modalCancel.removeEventListener('click', handleCancel);
        elements.modalClose.removeEventListener('click', handleCancel);
    };
    
    elements.modalConfirm.addEventListener('click', handlePasswordChange);
    elements.modalCancel.addEventListener('click', handleCancel);
    elements.modalClose.addEventListener('click', handleCancel);
    
    // Handle Enter key
    const handleKeyPress = (e) => {
        if (e.key === 'Enter') {
            handlePasswordChange();
        }
    };
    
    setTimeout(() => {
        const passwordInput = document.getElementById('admin-new-password');
        const confirmInput = document.getElementById('admin-confirm-password');
        if (passwordInput) passwordInput.addEventListener('keypress', handleKeyPress);
        if (confirmInput) confirmInput.addEventListener('keypress', handleKeyPress);
    }, 100);
}

async function handleDeleteUser(userId, username, email) {
    const confirmed = await showModal(
        'Kullanıcı Sil',
        `"${username}" kullanıcısını silmek istediğinizden emin misiniz?\n\nBu işlem geri alınamaz ve kullanıcının tüm verileri silinecektir.`,
        'Sil',
        'İptal'
    );
    
    if (!confirmed) return;
    
    try {
        showToast('Kullanıcı siliniyor...', 'info');
        
        const result = await ipcRenderer.invoke('admin-delete-user', {
            userId,
            username,
            email
        });
        
        if (result.success) {
            showToast('Kullanıcı başarıyla silindi!', 'success');
            
            // Reload users list
            await loadUsers();
        } else {
            showToast(result.error || 'Kullanıcı silinemedi!', 'error');
        }
        
    } catch (error) {
        console.error('Delete user error:', error);
        showToast('Bir hata oluştu!', 'error');
    }
}

function showIpHistoryModal(userId, username, ipHistory) {
    const {
        ipHistoryModalOverlay,
        ipHistoryModalTitle,
        ipHistoryModalBody,
        ipHistoryClearBtn,
        ipHistoryModalCancel,
        ipHistoryModalClose
    } = elements;

    ipHistoryModalTitle.textContent = `IP Geçmişi: ${username}`;

    if (ipHistory.length === 0) {
        ipHistoryModalBody.innerHTML = '<p class="no-ip-history">Bu kullanıcı için IP geçmişi bulunmuyor.</p>';
        ipHistoryClearBtn.disabled = true;
    } else {
        const sortedHistory = [...ipHistory].sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
        ipHistoryModalBody.innerHTML = `
            <div class="ip-history-list">
                ${sortedHistory.map((entry, index) => {
                    const date = new Date(entry.timestamp).toLocaleDateString('tr-TR');
                    const time = new Date(entry.timestamp).toLocaleTimeString('tr-TR');
                    const isRecent = index === 0;
                    return `
                        <div class="ip-history-item ${isRecent ? 'recent' : ''}">
                            <div class="ip-history-header">
                                <span class="ip-address">📍 ${entry.ip}</span>
                                ${isRecent ? '<span class="current-badge">Son Giriş</span>' : ''}
                            </div>
                            <div class="ip-history-details">
                                <small>🕒 ${date} ${time}</small>
                                <small>💻 ${entry.device || 'Bilinmiyor'}</small>
                                <small>🖥️ ${entry.platform || 'Bilinmiyor'}</small>
                            </div>
                        </div>
                    `;
                }).join('')}
            </div>
        `;
        ipHistoryClearBtn.disabled = false;
    }

    ipHistoryModalOverlay.classList.add('active');

    const handleClearHistory = async () => {
        // Hide the current modal before showing the confirmation
        ipHistoryModalOverlay.style.display = 'none';

        const confirmed = await showModal(
            'IP Geçmişini Temizle',
            `"${username}" kullanıcısının tüm IP geçmişini silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.`,
            'Evet, Temizle',
            'İptal'
        );

        if (confirmed) {
            // If confirmed, proceed with clearing and keep the history modal closed
            try {
                showToast('IP geçmişi temizleniyor...', 'info');
                const result = await ipcRenderer.invoke('admin-clear-ip-history', { userId });
                if (result.success) {
                    showToast(result.message, 'success');
                    closeModal(); // This will just clean up listeners
                    loadUsers(); // Refresh the user list
                } else {
                    showToast(result.error, 'error');
                    ipHistoryModalOverlay.style.display = 'flex'; // Show it again on error
                }
            } catch (error) {
                showToast(`Bir hata oluştu: ${error.message}`, 'error');
                ipHistoryModalOverlay.style.display = 'flex'; // Show it again on error
            }
        } else {
            // If cancelled, show the IP history modal again
            ipHistoryModalOverlay.style.display = 'flex';
        }
    };
    
    const closeModal = () => {
        ipHistoryModalOverlay.classList.remove('active');
        ipHistoryClearBtn.removeEventListener('click', handleClearHistory);
        ipHistoryModalCancel.removeEventListener('click', closeModal);
        ipHistoryModalClose.removeEventListener('click', closeModal);
    };

    ipHistoryClearBtn.addEventListener('click', handleClearHistory);
    ipHistoryModalCancel.addEventListener('click', closeModal);
    ipHistoryModalClose.addEventListener('click', closeModal);
}

// Load application settings into admin panel
async function loadAdminSettings() {
    try {
        const dbUrlInput = document.getElementById('db-url-template-input');
        if (!dbUrlInput) {
            console.log('Admin settings input not found, skipping load.');
            return;
        }

        const result = await ipcRenderer.invoke('admin-get-settings');
        if (result.success) {
            dbUrlInput.value = result.settings.downloadUrlTemplate;
            console.log('Application settings loaded into admin panel.');
        } else {
            showToast(result.error || 'Ayarlar yüklenemedi!', 'error');
        }
    } catch (error) {
        console.error('Load admin settings error:', error);
        showToast('Ayarlar yüklenirken bir hata oluştu!', 'error');
    }
}

// Handle saving application settings from admin panel
async function handleSaveSettings(e) {
    e.preventDefault();
    const dbUrlInput = document.getElementById('db-url-template-input');
    const newUrlTemplate = dbUrlInput.value.trim();

    if (!newUrlTemplate || !newUrlTemplate.includes('{appId}')) {
        showToast('Geçersiz URL şablonu. Şablon "{appId}" metnini içermelidir!', 'error');
        return;
    }

    try {
        showToast('Ayarlar güncelleniyor...', 'info');
        const result = await ipcRenderer.invoke('admin-update-settings', {
            downloadUrlTemplate: newUrlTemplate
        });

        if (result.success) {
            showToast(result.message, 'success');
        } else {
            showToast(result.error || 'Ayarlar güncellenemedi!', 'error');
        }
    } catch (error) {
        console.error('Save settings error:', error);
        showToast('Ayarlar güncellenirken bir hata oluştu!', 'error');
    }
}

// Profile Tab Functions
function initializeProfileTab() {
    // Get current user from localStorage
    const userStr = localStorage.getItem('currentUser');
    if (userStr) {
        currentUser = JSON.parse(userStr);
        loadUserProfile();
        initializeProfileEventListeners();
    }
}

function initializeProfileEventListeners() {
    // Change password form
    const changePasswordForm = document.getElementById('change-password-form');
    if (changePasswordForm) {
        changePasswordForm.addEventListener('submit', handleChangePassword);
    }
}

async function loadUserProfile() {
    try {
        // Get fresh user data from database
        const result = await ipcRenderer.invoke('get-current-user', currentUser);
        
        if (result.success) {
            const user = result.user;
            
            // Update profile display
            document.getElementById('profile-username').textContent = user.username;
            document.getElementById('profile-email').textContent = user.email;
            
            // Update role badge
            const roleBadge = document.getElementById('profile-role');
            if (roleBadge) {
                roleBadge.className = `role-badge ${user.role === 'admin' ? 'role-admin' : 'role-user'}`;
                roleBadge.textContent = user.role === 'admin' ? '👑 Admin' : '👤 Kullanıcı';
            }
            
            // Format dates
            const createdDate = user.createdAt ? new Date(user.createdAt).toLocaleDateString('tr-TR') : 'Bilinmiyor';
            const lastLoginDate = user.lastLogin ? new Date(user.lastLogin).toLocaleDateString('tr-TR') : 'Hiç giriş yapmamış';
            
            document.getElementById('profile-created').textContent = createdDate;
            document.getElementById('profile-last-login').textContent = lastLoginDate;
            
        } else {
            showToast('Kullanıcı bilgileri yüklenemedi!', 'error');
        }
        
    } catch (error) {
        console.error('Load profile error:', error);
        showToast('Profil bilgileri yüklenirken hata oluştu!', 'error');
    }
}

async function handleChangePassword(e) {
    e.preventDefault();
    
    const currentPassword = document.getElementById('current-password').value;
    const newPassword = document.getElementById('new-password').value;
    const confirmPassword = document.getElementById('confirm-password').value;
    
    // Validation
    if (!currentPassword || !newPassword || !confirmPassword) {
        showToast('Lütfen tüm alanları doldurun!', 'error');
        return;
    }
    
    if (newPassword.length < 6) {
        showToast('Yeni şifre en az 6 karakter olmalıdır!', 'error');
        return;
    }
    
    if (newPassword !== confirmPassword) {
        showToast('Yeni şifreler eşleşmiyor!', 'error');
        return;
    }
    
    try {
        showToast('Şifre değiştiriliyor...', 'info');
        
        const result = await ipcRenderer.invoke('change-password', {
            currentPassword,
            newPassword,
            currentUser
        });
        
        if (result.success) {
            showToast('Şifre başarıyla değiştirildi! Diğer cihazlardaki oturumlar sonlandırıldı.', 'success');
            
            // Update current user with new session token
            if (result.newSessionToken) {
                currentUser.sessionToken = result.newSessionToken;
                localStorage.setItem('currentUser', JSON.stringify(currentUser));
            }
            
            // Clear form
            document.getElementById('current-password').value = '';
            document.getElementById('new-password').value = '';
            document.getElementById('confirm-password').value = '';
            
            // Reload profile to update last login
            await loadUserProfile();
            
        } else {
            showToast(result.error || 'Şifre değiştirilemedi!', 'error');
        }
        
    } catch (error) {
        console.error('Change password error:', error);
        showToast('Bir hata oluştu!', 'error');
    }
}

// Initialize everything when DOM is loaded - SINGLE INITIALIZATION
document.addEventListener('DOMContentLoaded', () => {
    initializeTabNavigation();
    initializeEventListeners();
    initializeDragAndDrop();
    initializeApp();
    
    // Initialize admin panel and profile tab after a short delay to ensure user data is loaded
    setTimeout(() => {
        console.log('Initializing admin panel and profile tab...');
        initializeAdminPanel();
        initializeProfileTab();
        loadAdminSettings(); // Load settings for admin
    }, 2000); // Increased delay to 2 seconds
});

// Smart search functionality
let searchTimeout = null;
let selectedAutocompleteIndex = -1;
let autocompleteResults = [];

function handleSmartGameSearch() {
    const input = document.getElementById('download-game-search');
    const query = input.value.trim();
    
    // Clear previous timeout
    if (searchTimeout) {
        clearTimeout(searchTimeout);
    }
    
    // Hide autocomplete and preview if query is empty
    if (query.length === 0) {
        hideAutocomplete();
        elements.gamePreview.style.display = 'none';
        return;
    }
    
    // Check if input is a number (Steam ID)
    if (/^\d+$/.test(query)) {
        // Handle as Steam ID
        hideAutocomplete();
        handleGamePreviewById(query);
        return;
    }
    
    // Handle as game name search
    if (query.length < 2) {
        hideAutocomplete();
        elements.gamePreview.style.display = 'none';
        return;
    }
    
    // Debounce search for game names
    searchTimeout = setTimeout(async () => {
        try {
            showAutocompleteLoading();
            
            const result = await ipcRenderer.invoke('search-games', query);
            
            if (result.success && result.games.length > 0) {
                showAutocompleteResults(result.games);
            } else {
                showAutocompleteNoResults();
            }
        } catch (error) {
            console.error('Game search error:', error);
            hideAutocomplete();
        }
    }, 300); // 300ms debounce
}

async function handleGamePreviewById(appId) {
    try {
        // Show loading state
        elements.gamePreview.style.display = 'flex';
        elements.gamePreviewName.textContent = 'Yükleniyor...';
        elements.gamePreviewId.textContent = `Steam ID: ${appId}`;
        elements.gamePreviewImg.src = '';
        elements.gamePreviewImg.style.display = 'none';
        
        // Get game info
        const gameName = await ipcRenderer.invoke('get-game-name', appId);
        const gameImageUrl = await ipcRenderer.invoke('get-game-image', appId);
        
        if (gameName) {
            // Update preview with game info
            elements.gamePreviewName.textContent = gameName;
            elements.gamePreviewId.textContent = `Steam ID: ${appId}`;
            
            // Load game image
            if (gameImageUrl) {
                elements.gamePreviewImg.src = gameImageUrl;
                elements.gamePreviewImg.style.display = 'block';
                elements.gamePreviewImg.onerror = () => {
                    elements.gamePreviewImg.style.display = 'none';
                };
            }
        } else {
            // Game not found
            elements.gamePreviewName.textContent = 'Oyun bulunamadı';
            elements.gamePreviewId.textContent = `Steam ID: ${appId}`;
            elements.gamePreviewImg.style.display = 'none';
        }
        
    } catch (error) {
        console.error('Game preview error:', error);
        elements.gamePreviewName.textContent = 'Bilgi alınamadı';
        elements.gamePreviewId.textContent = `Steam ID: ${appId}`;
        elements.gamePreviewImg.style.display = 'none';
    }
}

function showAutocompleteLoading() {
    const dropdown = document.getElementById('autocomplete-dropdown');
    dropdown.innerHTML = `
        <div class="autocomplete-loading">
            <div class="spinner"></div>
            Oyunlar aranıyor...
        </div>
    `;
    dropdown.classList.add('active');
    selectedAutocompleteIndex = -1;
    autocompleteResults = [];
}

function showAutocompleteResults(games) {
    const dropdown = document.getElementById('autocomplete-dropdown');
    autocompleteResults = games;
    selectedAutocompleteIndex = -1;
    
    dropdown.innerHTML = games.map((game, index) => `
        <div class="autocomplete-item" data-index="${index}" data-app-id="${game.id}" data-game-name="${game.name}">
            <div class="autocomplete-item-image">
                <img src="${game.image}" alt="${game.name}" onerror="this.style.display='none'">
            </div>
            <div class="autocomplete-item-info">
                <div class="autocomplete-item-name">${game.name}</div>
                <div class="autocomplete-item-id">ID: ${game.id}</div>
            </div>
        </div>
    `).join('');
    
    dropdown.classList.add('active');
    
    // Add click listeners
    dropdown.querySelectorAll('.autocomplete-item').forEach(item => {
        item.addEventListener('click', () => {
            selectAutocompleteItem(item);
        });
    });
}

function showAutocompleteNoResults() {
    const dropdown = document.getElementById('autocomplete-dropdown');
    dropdown.innerHTML = `
        <div class="autocomplete-no-results">
            Oyun bulunamadı
        </div>
    `;
    dropdown.classList.add('active');
    selectedAutocompleteIndex = -1;
    autocompleteResults = [];
}

function hideAutocomplete() {
    setTimeout(() => {
        const dropdown = document.getElementById('autocomplete-dropdown');
        dropdown.classList.remove('active');
        selectedAutocompleteIndex = -1;
    }, 150); // Small delay to allow click events
}

function selectAutocompleteItem(item) {
    const appId = item.dataset.appId;
    const gameName = item.dataset.gameName;
    
    // Fill the input with game name
    const input = document.getElementById('download-game-search');
    input.value = gameName;
    
    // Hide autocomplete
    hideAutocomplete();
    
    // Show game preview
    showGamePreviewFromSearch(appId, gameName);
}

function handleAutocompleteKeydown(e) {
    const dropdown = document.getElementById('autocomplete-dropdown');
    if (!dropdown.classList.contains('active') || autocompleteResults.length === 0) {
        return;
    }
    
    const items = dropdown.querySelectorAll('.autocomplete-item');
    
    switch (e.key) {
        case 'ArrowDown':
            e.preventDefault();
            selectedAutocompleteIndex = Math.min(selectedAutocompleteIndex + 1, items.length - 1);
            updateAutocompleteSelection(items);
            break;
            
        case 'ArrowUp':
            e.preventDefault();
            selectedAutocompleteIndex = Math.max(selectedAutocompleteIndex - 1, -1);
            updateAutocompleteSelection(items);
            break;
            
        case 'Enter':
            e.preventDefault();
            if (selectedAutocompleteIndex >= 0 && items[selectedAutocompleteIndex]) {
                selectAutocompleteItem(items[selectedAutocompleteIndex]);
            }
            break;
            
        case 'Escape':
            hideAutocomplete();
            break;
    }
}

function updateAutocompleteSelection(items) {
    items.forEach((item, index) => {
        item.classList.toggle('selected', index === selectedAutocompleteIndex);
    });
    
    // Scroll selected item into view
    if (selectedAutocompleteIndex >= 0 && items[selectedAutocompleteIndex]) {
        items[selectedAutocompleteIndex].scrollIntoView({
            block: 'nearest'
        });
    }
}

async function showGamePreviewFromSearch(appId, gameName) {
    try {
        // Update game preview
        elements.gamePreviewName.textContent = gameName;
        elements.gamePreviewId.textContent = `Steam ID: ${appId}`;
        
        // Load game image
        const gameImageUrl = await ipcRenderer.invoke('get-game-image', appId);
        if (gameImageUrl) {
            elements.gamePreviewImg.src = gameImageUrl;
            elements.gamePreviewImg.style.display = 'block';
            elements.gamePreviewImg.onerror = () => {
                elements.gamePreviewImg.style.display = 'none';
            };
        }
        
        // Show preview
        elements.gamePreview.style.display = 'flex';
        
        // Update download app ID input for compatibility
        elements.downloadAppIdInput.value = appId;
        
    } catch (error) {
        console.error('Game preview error:', error);
    }
}