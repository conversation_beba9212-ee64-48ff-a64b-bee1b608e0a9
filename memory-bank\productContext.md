# Steam Delisi - Ürün Bağlam<PERSON>

## 🎮 Problem Tanımı
Steam oyunlarının manifest dosyalarını manuel olarak bulmak ve işlemek zor ve zaman alıcı. Kullanıcılar:
- Steam'in depotcache klasöründe kayboluyorlar
- Hangi manifest'in güncel olduğunu bilmiyorlar
- Lua script formatını manuel yazmak zorunda kalıyorlar
- Eski sürüm manifest'leri yanlışlıkla kullanıyorlar

## 💡 Çözüm
Otomatik Steam manifest işleyici uygulama:
- Steam kurulumunu otomatik bulur
- Manifest dosyalarını tarar ve gruplar
- En güncel sürümleri otomatik seçer
- Doğru formatta Lua script oluşturur
- DecryptionKey'leri Steam config'den çeker

## 🎯 Hede<PERSON>ı<PERSON>
- Steam oyun modifikasyonu yapan geliştiriciler
- Oyun backup/restore işlemleri yapan kullanı<PERSON>ılar
- Steam depot analizi yapan a<PERSON>ırmacılar
- Oyun sürüm kontrolü yapan topluluklar

## 🌟 Değer Önerisi
- **Zaman Tasarrufu**: Manuel işlem yerine otomatik
- **Hata Önleme**: Yanlış manifest seçimini engeller
- **Kullanım Kolaylığı**: Modern, sezgisel arayüz
- **Güvenilirlik**: Steam'in kendi verilerini kullanır
- **Esneklik**: En güncel veya tüm manifest seçenekleri

## 🔧 Teknik Avantajlar
- Steam API entegrasyonu
- VDF dosya okuma
- Regex pattern matching
- Otomatik DecryptionKey çekme
- PyInstaller ile tek dosya dağıtım