<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Steam Delisi - Giriş</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            width: 400px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .logo {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin-bottom: 15px;
        }

        .logo h1 {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 5px;
            background: linear-gradient(45deg, #fff, #e0e0e0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .logo p {
            font-size: 14px;
            opacity: 0.8;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            font-size: 14px;
        }

        .form-group input {
            width: 100%;
            padding: 12px 16px;
            border: none;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 16px;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .form-group input:focus {
            outline: none;
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .form-group input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .login-btn {
            width: 100%;
            padding: 14px;
            border: none;
            border-radius: 10px;
            background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 10px;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .login-btn:active {
            transform: translateY(0);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .error-message {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid rgba(244, 67, 54, 0.4);
            color: #ffcdd2;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            display: none;
        }

        .loading {
            display: none;
            text-align: center;
            margin-top: 20px;
        }

        .loading-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .footer {
            text-align: center;
            margin-top: 30px;
            font-size: 12px;
            opacity: 0.7;
        }

        /* Remember Me Checkbox */
        .remember-me-label {
            display: flex;
            align-items: center;
            cursor: pointer;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 0;
            padding: 8px 0;
        }

        .remember-me-label input[type="checkbox"] {
            width: 16px;
            height: 16px;
            margin-right: 8px;
            accent-color: #667eea;
            cursor: pointer;
        }

        .remember-me-label:hover {
            color: rgba(255, 255, 255, 1);
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <img src="logo.png" alt="Steam Delisi Logo">
            <h1>Steam Delisi</h1>
            <p>Giriş Yapın</p>
        </div>

        <div class="error-message" id="errorMessage"></div>

        <form id="loginForm">
            <div class="form-group">
                <label for="identifier">Kullanıcı Adı veya E-posta</label>
                <input type="text" id="identifier" name="identifier" placeholder="Kullanıcı adınızı veya e-postanızı girin" required>
            </div>

            <div class="form-group">
                <label for="password">Şifre</label>
                <input type="password" id="password" name="password" placeholder="Şifrenizi girin" required>
            </div>

            <div class="form-group">
                <label class="remember-me-label">
                    <input type="checkbox" id="rememberMe" name="rememberMe">
                    Beni Hatırla
                </label>
            </div>

            <button type="submit" class="login-btn" id="loginBtn">
                Giriş Yap
            </button>
        </form>

        <div class="loading" id="loading">
            <div class="loading-spinner"></div>
            Giriş yapılıyor...
        </div>

        <div class="footer">
            <p>Steam Delisi - Apk Delisi</p>
        </div>
    </div>

    <script>
        const { ipcRenderer } = require('electron');

        const loginForm = document.getElementById('loginForm');
        const loginBtn = document.getElementById('loginBtn');
        const loading = document.getElementById('loading');
        const errorMessage = document.getElementById('errorMessage');
        const rememberMeCheckbox = document.getElementById('rememberMe');

        // Check for auto-login and saved credentials on page load
        document.addEventListener('DOMContentLoaded', () => {
            // First check for auto-login
            const autoLoginStr = localStorage.getItem('autoLogin');
            if (autoLoginStr) {
                try {
                    const autoLogin = JSON.parse(autoLoginStr);
                    const now = Date.now();
                    const dayInMs = 24 * 60 * 60 * 1000; // 24 hours
                    
                    // Check if auto-login is still valid (within 30 days)
                    if (autoLogin.rememberMe && (now - autoLogin.timestamp) < (30 * dayInMs)) {
                        // Auto-login is valid, restore user session and redirect
                        localStorage.setItem('currentUser', JSON.stringify(autoLogin.user));
                        console.log('Auto-login successful, redirecting to main app...');
                        window.location.href = 'index.html';
                        return;
                    } else {
                        // Auto-login expired, remove it
                        localStorage.removeItem('autoLogin');
                    }
                } catch (error) {
                    console.error('Error parsing auto-login data:', error);
                    localStorage.removeItem('autoLogin');
                }
            }
            
            // If no auto-login, check for saved credentials to fill form
            const savedCredentials = localStorage.getItem('savedCredentials');
            if (savedCredentials) {
                try {
                    const { identifier, rememberMe } = JSON.parse(savedCredentials);
                    if (rememberMe) {
                        document.getElementById('identifier').value = identifier;
                        document.getElementById('rememberMe').checked = true;
                        // Focus on password field
                        document.getElementById('password').focus();
                    }
                } catch (error) {
                    console.error('Error loading saved credentials:', error);
                    localStorage.removeItem('savedCredentials');
                }
            }
        });

        loginForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const identifier = document.getElementById('identifier').value.trim();
            const password = document.getElementById('password').value;
            const rememberMe = rememberMeCheckbox.checked;

            if (!identifier || !password) {
                showError('Lütfen tüm alanları doldurun!');
                return;
            }

            setLoading(true);
            hideError();

            try {
                const result = await ipcRenderer.invoke('auth-login', {
                    identifier,
                    password
                });

                if (result.success) {
                    // Store user data
                    localStorage.setItem('currentUser', JSON.stringify(result.user));
                    
                    // Handle "Remember Me" option
                    if (rememberMe) {
                        // Save complete session for auto-login
                        localStorage.setItem('autoLogin', JSON.stringify({
                            user: result.user,
                            timestamp: Date.now(),
                            rememberMe: true
                        }));
                        // Also save credentials for form filling
                        localStorage.setItem('savedCredentials', JSON.stringify({
                            identifier,
                            rememberMe: true
                        }));
                    } else {
                        // Remove saved data if "Remember Me" is unchecked
                        localStorage.removeItem('autoLogin');
                        localStorage.removeItem('savedCredentials');
                    }
                    
                    // Show success message with security info
                    showError('Giriş başarılı! Diğer cihazlardaki oturumlar sonlandırıldı.', 'success');
                    
                    // Redirect to main app after a short delay
                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 1500);
                } else {
                    showError(result.error || 'Giriş başarısız!');
                }
            } catch (error) {
                console.error('Login error:', error);
                showError('Bir hata oluştu. Lütfen tekrar deneyin.');
            } finally {
                setLoading(false);
            }
        });

        function setLoading(isLoading) {
            if (isLoading) {
                loginBtn.disabled = true;
                loginBtn.style.display = 'none';
                loading.style.display = 'block';
            } else {
                loginBtn.disabled = false;
                loginBtn.style.display = 'block';
                loading.style.display = 'none';
            }
        }

        function showError(message, type = 'error') {
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
            
            if (type === 'success') {
                errorMessage.style.background = 'rgba(76, 175, 80, 0.2)';
                errorMessage.style.borderColor = 'rgba(76, 175, 80, 0.4)';
                errorMessage.style.color = '#c8e6c9';
            } else {
                errorMessage.style.background = 'rgba(244, 67, 54, 0.2)';
                errorMessage.style.borderColor = 'rgba(244, 67, 54, 0.4)';
                errorMessage.style.color = '#ffcdd2';
            }
        }

        function hideError() {
            errorMessage.style.display = 'none';
        }
    </script>
</body>
</html>