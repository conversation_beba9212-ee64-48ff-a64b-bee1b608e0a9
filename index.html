<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Steam Delisi</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <div class="logo-section">
                    <div class="logo-icon">
                        <img src="logo.png" alt="Steam Lua & Manifest Exporter" class="app-logo">
                    </div>
                    <h1 class="app-title">Steam Delisi</h1>
                </div>
                <div class="header-actions">
                    <button class="btn btn-warning header-btn" id="header-restart-steam-btn">
                        <span class="btn-icon">🔄</span>
                        Steam'i Yeniden Başlat
                    </button>
                </div>
            </div>
        </header>

        <!-- Navigation Tabs -->
        <nav class="tab-navigation">
            <div class="tab-buttons">
                <button class="tab-button active" data-tab="export">
                    <span class="tab-icon">📤</span>
                    Dışa Aktar
                </button>
                <button class="tab-button" data-tab="import">
                    <span class="tab-icon">📥</span>
                    İçe Aktar
                </button>
                <button class="tab-button" data-tab="download">
                    <span class="tab-icon">⬇️</span>
                    Otomatik Oyun İndir
                </button>
                <button class="tab-button" data-tab="games">
                    <span class="tab-icon">🎮</span>
                    Yüklü Oyunlar
                </button>
                <button class="tab-button" data-tab="profile">
                    <span class="tab-icon">👤</span>
                    Profil
                </button>
                <button class="tab-button admin-only" data-tab="admin" style="display: none;">
                    <span class="tab-icon">⚙️</span>
                    Admin Paneli
                </button>
            </div>
            <div class="user-info" id="user-info">
                <span class="user-welcome">Hoş geldin, <span id="username">Kullanıcı</span></span>
                <button class="btn btn-small btn-secondary" id="logout-btn">
                    <span class="btn-icon">🚪</span>
                    Çıkış
                </button>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Export Tab -->
            <div class="tab-content active" id="export-tab">
                <div class="export-section">
                    <div class="input-panel">
                        <div class="input-row">
                            <div class="input-group">
                                <label for="app-id">Steam Oyun ID:</label>
                                <input type="text" id="app-id" placeholder="Örn: 1971870" />
                            </div>
                            
                            <div class="input-group">
                                <label>Manifest Seçimi:</label>
                                <div class="radio-group">
                                    <label class="radio-label">
                                        <input type="radio" name="manifest-mode" value="latest" checked />
                                        <span class="radio-custom"></span>
                                        En Güncel
                                    </label>
                                    <label class="radio-label">
                                        <input type="radio" name="manifest-mode" value="all" />
                                        <span class="radio-custom"></span>
                                        Tüm Manifestler
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="action-buttons">
                            <button class="btn btn-primary" id="scan-btn">
                                <span class="btn-icon">🔍</span>
                                Manifest Tara
                            </button>
                            <button class="btn btn-success" id="create-btn" disabled>
                                <span class="btn-icon">⚡</span>
                                Lua Oluştur
                            </button>
                            <button class="btn btn-secondary" id="open-folder-btn">
                                <span class="btn-icon">📂</span>
                                Klasörü Aç
                            </button>
                        </div>

                        <div class="manual-steam-path" id="manual-steam-path-container" style="display: none;">
                            <p>⚠️ Steam yolu otomatik olarak bulunamadı.</p>
                            <button class="btn btn-warning" id="manual-steam-path-btn">
                                <span class="btn-icon">📁</span>
                                Steam Klasörünü Manuel Seç
                            </button>
                        </div>

                        <div class="folder-inputs">
                            <div class="input-group">
                                <label for="manifest-folder">Manifest Klasörü:</label>
                                <div class="folder-input">
                                    <input type="text" id="manifest-folder" readonly />
                                    <div class="folder-status" id="manifest-folder-status">
                                        <span class="status-icon">⏳</span>
                                        <span class="status-text">Kontrol ediliyor...</span>
                                    </div>
                                    <button class="btn btn-small" id="browse-manifest-btn">Gözat</button>
                                </div>
                            </div>
                            
                            <div class="input-group">
                                <label for="config-file">Config.vdf Dosyası:</label>
                                <div class="folder-input">
                                    <input type="text" id="config-file" readonly />
                                    <div class="folder-status" id="config-file-status">
                                        <span class="status-icon">⏳</span>
                                        <span class="status-text">Kontrol ediliyor...</span>
                                    </div>
                                    <button class="btn btn-small" id="browse-config-btn">Gözat</button>
                                </div>
                            </div>
                            
                            <div class="input-group">
                                <label for="output-folder">Çıktı Klasörü:</label>
                                <div class="folder-input">
                                    <input type="text" id="output-folder" readonly />
                                    <button class="btn btn-small" id="browse-output-btn">Gözat</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="results-panel">
                        <div class="results-header">
                            <h3>📊 İşlem Sonuçları</h3>
                            <button class="btn btn-small" id="clear-log-btn">Temizle</button>
                        </div>
                        <div class="log-container" id="log-container">
                            <div class="log-message info">
                                <span class="log-time">[00:00:00]</span>
                                <span class="log-icon">ℹ️</span>
                                <span class="log-text">Steam Delisi'ne Hoş Geldiniz!</span>
                            </div>
                            <div class="log-message info">
                                <span class="log-time">[00:00:00]</span>
                                <span class="log-icon">ℹ️</span>
                                <span class="log-text">Steam manifest dosyalarınızı hızlı ve kolay şekilde işleyin.</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Import Tab -->
            <div class="tab-content" id="import-tab">
                <div class="import-section">
                    <div class="import-header">
                        <h2>📥 Steam'e İçe Aktar</h2>
                        <p>Lua ve manifest dosyalarını sürükleyip bırakarak Steam'e aktarın</p>
                    </div>
                    
                    <div class="drop-zone" id="drop-zone">
                        <div class="drop-zone-content">
                            <div class="drop-icon">📁</div>
                            <h3>Dosyaları Buraya Sürükleyin</h3>
                            <p>Lua dosyaları (.lua) ve manifest dosyaları (.manifest) desteklenir</p>
                            <div class="drop-zone-or">veya</div>
                            <button class="btn btn-primary" id="select-files-btn">
                                <span class="btn-icon">📂</span>
                                Dosya Seç
                            </button>
                        </div>
                        <div class="drop-zone-overlay" id="drop-overlay">
                            <div class="drop-overlay-content">
                                <div class="drop-overlay-icon">📥</div>
                                <h3>Dosyaları Bırakın</h3>
                            </div>
                        </div>
                    </div>
                    
                    <div class="import-files-list" id="import-files-list" style="display: none;">
                        <div class="import-files-header">
                            <h3>📋 Seçilen Dosyalar</h3>
                            <button class="btn btn-small btn-secondary" id="clear-files-btn">Temizle</button>
                        </div>
                        <div class="files-container" id="files-container"></div>
                        <div class="import-actions">
                            <button class="btn btn-success" id="import-files-btn">
                                <span class="btn-icon">🚀</span>
                                Steam'e Aktar
                            </button>
                        </div>
                    </div>
                    
                    <div class="import-info">
                        <div class="info-card">
                            <h4>💡 Nasıl Kullanılır?</h4>
                            <ul>
                                <li>Lua dosyalarını (.lua) ve manifest dosyalarını (.manifest) seçin</li>
                                <li>Dosyalar otomatik olarak analiz edilecek</li>
                                <li>"Steam'e Aktar" butonuna tıklayın</li>
                                <li>Dosyalar doğru Steam klasörlerine kopyalanacak</li>
                            </ul>
                        </div>
                        <div class="info-card">
                            <h4>⚠️ Önemli Notlar</h4>
                            <ul>
                                <li>Aynı ID'li oyunlar varsa üzerine yazılacak</li>
                                <li>Steam kapalıyken işlem yapmanız önerilir</li>
                                <li>İşlem sonrası Steam'i yeniden başlatın</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Download Tab -->
            <div class="tab-content" id="download-tab">
                <div class="download-section">
                    <div class="download-header">
                        <h2>⬇️ Oyun İndir</h2>
                        <p>Steam Database'den oyunları doğrudan indirin</p>
                    </div>
                    
                    <div class="download-panel">
                        <div class="download-input-section">
                            <div class="input-group">
                                <label for="download-game-search">Steam Oyun ID veya Oyun Adı:</label>
                                <div class="autocomplete-container">
                                    <input type="text" id="download-game-search" placeholder="Örn: 730, Counter-Strike, Half-Life" />
                                    <div class="autocomplete-dropdown" id="autocomplete-dropdown"></div>
                                </div>
                            </div>
                            
                            <div class="game-preview" id="game-preview" style="display: none;">
                                <div class="game-preview-image">
                                    <img id="game-preview-img" src="" alt="Game Cover" />
                                </div>
                                <div class="game-preview-info">
                                    <h4 id="game-preview-name">Oyun Adı</h4>
                                    <p id="game-preview-id">Steam ID: </p>
                                </div>
                            </div>
                            
                            <div class="download-actions">
                                <button class="btn btn-primary" id="download-game-btn">
                                    <span class="btn-icon">⬇️</span>
                                    Oyunu İndir
                                </button>
                                <button class="btn btn-success" id="download-and-import-btn">
                                    <span class="btn-icon">🚀</span>
                                    İndir ve Steam'e Aktar
                                </button>
                            </div>
                        </div>
                        
                        <div class="download-progress" id="download-progress" style="display: none;">
                            <div class="progress-header">
                                <h3>📥 İndiriliyor...</h3>
                                <span class="progress-status" id="progress-status">Hazırlanıyor...</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" id="progress-fill"></div>
                            </div>
                            <div class="progress-details">
                                <span class="progress-text" id="progress-text">0%</span>
                                <span class="progress-size" id="progress-size">0 / 0 MB</span>
                            </div>
                            <div class="progress-actions" id="progress-actions" style="display: none;">
                                <button class="btn btn-success" id="progress-import-btn">
                                    <span class="btn-icon">🚀</span>
                                    Steam'e Aktar
                                </button>
                            </div>
                        </div>
                        
                        <div class="download-results" id="download-results" style="display: none;">
                            <div class="results-header">
                                <h3>📊 İndirme Sonuçları</h3>
                                <button class="btn btn-small" id="clear-download-log-btn">Temizle</button>
                            </div>
                            <div class="log-container" id="download-log-container">
                                <div class="log-message info">
                                    <span class="log-time">[00:00:00]</span>
                                    <span class="log-icon">ℹ️</span>
                                    <span class="log-text">Oyun indirme hazır!</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="download-info">
                        <div class="info-card">
                            <h4>💡 Nasıl Kullanılır?</h4>
                            <ul>
                                <li>Steam oyun ID'sini girin (örn: 10, 440, 730)</li>
                                <li>"Oyunu İndir" ile sadece indirin</li>
                                <li>"İndir ve Steam'e Aktar" ile otomatik olarak Steam'e aktarın</li>
                                <li>İndirilen dosyalar otomatik olarak çıkarılacak</li>
                            </ul>
                        </div>
                        <div class="info-card">
                            <h4>🌐 Database Bilgisi</h4>
                            <ul>
                                <li>Kaynak: Steam Database (S3)</li>
                                <li>Format: ZIP arşivi</li>
                                <li>İçerik: Lua ve manifest dosyaları</li>
                                <li>Güncellik: Düzenli olarak güncellenir</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Games Tab -->
            <div class="tab-content" id="games-tab">
                <div class="games-section">
                    <div class="games-header">
                        <h2>🎮 Steam'de Yüklü Oyunlar</h2>
                        <button class="btn btn-primary" id="refresh-games-btn">
                            <span class="btn-icon">🔄</span>
                            Oyunları Yenile
                        </button>
                    </div>
                    
                    <div class="games-grid" id="games-grid">
                        <div class="loading-spinner" id="games-loading">
                            <div class="spinner"></div>
                            <p>Oyunlar yükleniyor...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Tab -->
            <div class="tab-content" id="profile-tab">
                <div class="profile-section">
                    <div class="profile-header">
                        <h2>👤 Profil Ayarları</h2>
                        <p>Hesap bilgilerinizi yönetin ve şifrenizi değiştirin</p>
                    </div>
                    
                    <div class="profile-panels">
                        <!-- User Info Panel -->
                        <div class="profile-panel">
                            <div class="panel-header">
                                <h3>📋 Hesap Bilgileri</h3>
                            </div>
                            
                            <div class="user-info-display">
                                <div class="info-row">
                                    <div class="info-label">Kullanıcı Adı:</div>
                                    <div class="info-value" id="profile-username">-</div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">E-posta:</div>
                                    <div class="info-value" id="profile-email">-</div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">Rol:</div>
                                    <div class="info-value">
                                        <span class="role-badge" id="profile-role">-</span>
                                    </div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">Kayıt Tarihi:</div>
                                    <div class="info-value" id="profile-created">-</div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">Son Giriş:</div>
                                    <div class="info-value" id="profile-last-login">-</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Change Password Panel -->
                        <div class="profile-panel">
                            <div class="panel-header">
                                <h3>🔒 Şifre Değiştir</h3>
                            </div>
                            
                            <div class="change-password-form">
                                <form id="change-password-form">
                                    <div class="form-group">
                                        <label for="current-password">Mevcut Şifre:</label>
                                        <input type="password" id="current-password" placeholder="Mevcut şifrenizi girin" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="new-password">Yeni Şifre:</label>
                                        <input type="password" id="new-password" placeholder="Yeni şifrenizi girin (min. 6 karakter)" required minlength="6">
                                    </div>
                                    <div class="form-group">
                                        <label for="confirm-password">Yeni Şifre Tekrar:</label>
                                        <input type="password" id="confirm-password" placeholder="Yeni şifrenizi tekrar girin" required minlength="6">
                                    </div>
                                    <div class="form-actions">
                                        <button type="submit" class="btn btn-primary">
                                            <span class="btn-icon">🔒</span>
                                            Şifreyi Değiştir
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Admin Panel Tab -->
            <div class="tab-content" id="admin-tab">
                <div class="admin-section">
                    <div class="admin-header">
                        <h2>⚙️ Admin Paneli</h2>
                        <p>Kullanıcı yönetimi ve sistem ayarları</p>
                    </div>
                    
                    <div class="admin-panels">
                        <!-- User Management Panel -->
                        <div class="admin-panel">
                            <div class="panel-header">
                                <h3>👥 Kullanıcı Yönetimi</h3>
                                <button class="btn btn-primary" id="refresh-users-btn">
                                    <span class="btn-icon">🔄</span>
                                    Kullanıcıları Yenile
                                </button>
                            </div>
                            
                            <!-- Add User Form -->
                            <div class="add-user-form">
                                <h4>➕ Yeni Kullanıcı Ekle</h4>
                                <form id="add-user-form">
                                    <div class="form-row">
                                        <div class="input-group">
                                            <label for="new-username">Kullanıcı Adı:</label>
                                            <input type="text" id="new-username" name="username" placeholder="Kullanıcı adı" required>
                                        </div>
                                        <div class="input-group">
                                            <label for="new-email">E-posta:</label>
                                            <input type="email" id="new-email" name="email" placeholder="E-posta adresi" required>
                                        </div>
                                    </div>
                                    <div class="form-row">
                                        <div class="input-group">
                                            <label for="new-password">Şifre:</label>
                                            <input type="password" id="new-password" name="password" placeholder="Şifre (min. 6 karakter)" required minlength="6">
                                        </div>
                                        <div class="input-group">
                                            <button type="submit" class="btn btn-success">
                                                <span class="btn-icon">➕</span>
                                                Kullanıcı Ekle
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            
                            <!-- Users List -->
                            <div class="users-list">
                                <h4>📋 Mevcut Kullanıcılar</h4>
                                <div class="users-table-container">
                                    <table class="users-table" id="users-table">
                                        <thead>
                                            <tr>
                                                <th>Kullanıcı Adı</th>
                                                <th>E-posta</th>
                                                <th>Rol</th>
                                                <th>Kayıt Tarihi</th>
                                                <th>Son Giriş</th>
                                                <th>İşlemler</th>
                                            </tr>
                                        </thead>
                                        <tbody id="users-table-body">
                                            <tr>
                                                <td colspan="6" class="loading-cell">
                                                    <div class="loading-spinner">
                                                        <div class="spinner"></div>
                                                        Kullanıcılar yükleniyor...
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Application Settings Panel -->
                        <div class="admin-panel">
                            <div class="panel-header">
                                <h3>🔧 Uygulama Ayarları</h3>
                            </div>
                            <div class="add-user-form">
                                <h4>⚙️ Genel Ayarlar</h4>
                                <form id="app-settings-form" onsubmit="event.preventDefault();">
                                    <div class="form-row">
                                        <div class="input-group">
                                            <label for="db-url-template-input">Veritabanı URL Şablonu</label>
                                            <input type="text" id="db-url-template-input" name="dbUrlTemplate" placeholder="https://ornek.com/data/{appId}.zip" required>
                                            <small class="form-text">
                                                Oyun indirme linkinin şablonu. URL, <strong>{appId}</strong> içermelidir.
                                            </small>
                                        </div>
                                    </div>
                                    <div class="form-actions">
                                        <button id="save-settings-btn" class="btn btn-primary" type="button">
                                            <span class="btn-icon">💾</span>
                                            Ayarları Kaydet
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="app-footer">
            <div class="footer-content">
                <span>Made by İsmail Akçay</span>
                <a href="#" onclick="require('electron').shell.openExternal('https://apkdelisi.net')" class="footer-link">apkdelisi.net</a>
            </div>
        </footer>
    </div>

    <!-- Toast Notifications -->
    <div class="toast-container" id="toast-container"></div>

    <!-- Confirmation Modal -->
    <div class="modal-overlay" id="modal-overlay">
        <div class="modal">
            <div class="modal-header">
                <h3 id="modal-title">Onay</h3>
                <button class="modal-close" id="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <p id="modal-message">Bu işlemi gerçekleştirmek istediğinizden emin misiniz?</p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="modal-cancel">İptal</button>
                <button class="btn btn-danger" id="modal-confirm">Onayla</button>
            </div>
        </div>
    </div>

    <!-- IP History Modal -->
    <div class="modal-overlay" id="ip-history-modal-overlay">
        <div class="modal ip-history-modal">
            <div class="modal-header">
                <h3 id="ip-history-modal-title">IP Geçmişi</h3>
                <button class="modal-close" id="ip-history-modal-close">&times;</button>
            </div>
            <div class="modal-body" id="ip-history-modal-body">
                <!-- IP list will be injected here -->
            </div>
            <div class="modal-footer">
                <button class="btn btn-danger" id="ip-history-clear-btn">
                    <span class="btn-icon">🗑️</span>
                    Geçmişi Temizle
                </button>
                <button class="btn btn-secondary" id="ip-history-modal-cancel">Kapat</button>
            </div>
        </div>
    </div>

    <!-- Security Notification Modal -->
    <div class="security-notification-overlay" id="securityNotificationOverlay">
        <div class="security-notification">
            <div class="security-notification-icon">
                <div class="security-icon-wrapper">
                    <span class="security-icon">🔒</span>
                </div>
            </div>
            <div class="security-notification-content">
                <h3 class="security-notification-title" id="securityNotificationTitle">Güvenlik Uyarısı</h3>
                <p class="security-notification-message" id="securityNotificationMessage">Oturumunuz güvenlik nedeniyle sonlandırıldı.</p>
            </div>
            <div class="security-notification-actions">
                <button class="btn btn-primary security-notification-btn" id="securityNotificationBtn">
                    <span class="btn-icon">✓</span>
                    Tamam
                </button>
            </div>
        </div>
    </div>

    <script>
        // Authentication check
        window.addEventListener('DOMContentLoaded', async () => {
            const { ipcRenderer } = require('electron');
            
            try {
                // Check for auto-login first
                const autoLoginStr = localStorage.getItem('autoLogin');
                if (autoLoginStr) {
                    try {
                        const autoLogin = JSON.parse(autoLoginStr);
                        const now = Date.now();
                        const dayInMs = 24 * 60 * 60 * 1000; // 24 hours
                        
                        // Check if auto-login is still valid (within 30 days)
                        if (autoLogin.rememberMe && (now - autoLogin.timestamp) < (30 * dayInMs)) {
                            // Auto-login is valid, restore user session
                            localStorage.setItem('currentUser', JSON.stringify(autoLogin.user));
                            console.log('Auto-login successful for user:', autoLogin.user.username);
                            // Continue with normal app initialization
                        } else {
                            // Auto-login expired, remove it
                            localStorage.removeItem('autoLogin');
                            localStorage.removeItem('currentUser');
                            window.location.href = 'login.html';
                            return;
                        }
                    } catch (error) {
                        console.error('Error parsing auto-login data:', error);
                        localStorage.removeItem('autoLogin');
                        localStorage.removeItem('currentUser');
                        window.location.href = 'login.html';
                        return;
                    }
                }
                
                // Check if user is logged in from localStorage
                const userStr = localStorage.getItem('currentUser');
                if (!userStr) {
                    // Redirect to login page
                    window.location.href = 'login.html';
                    return;
                }
                
                const user = JSON.parse(userStr);
                
                // Validate session token if available
                if (user.sessionToken) {
                    try {
                        const sessionResult = await ipcRenderer.invoke('validate-session', user);
                        if (!sessionResult.success) {
                            // Session is invalid, redirect to login
                            console.log('Session validation failed:', sessionResult.error);
                            localStorage.removeItem('currentUser');
                            localStorage.removeItem('autoLogin');
                            localStorage.removeItem('savedCredentials');
                            showSecurityNotification('Oturumunuz sonlandırıldı!', 'Hesabınız başka bir cihazdan açılmış olabilir.', () => {
                                window.location.href = 'login.html';
                            });
                            return;
                        }
                    } catch (error) {
                        console.error('Session validation error:', error);
                        // On error, continue but log the issue
                    }
                }
                
                // Set up periodic session validation (every 30 seconds)
                setInterval(async () => {
                    try {
                        const currentUserStr = localStorage.getItem('currentUser');
                        if (currentUserStr) {
                            const currentUser = JSON.parse(currentUserStr);
                            if (currentUser.sessionToken) {
                                const sessionResult = await ipcRenderer.invoke('validate-session', currentUser);
                                if (!sessionResult.success) {
                                    // Session is invalid, redirect to login
                                    console.log('Periodic session check failed:', sessionResult.error);
                                    localStorage.removeItem('currentUser');
                                    localStorage.removeItem('autoLogin');
                                    localStorage.removeItem('savedCredentials');
                                    showSecurityNotification('Oturumunuz sonlandırıldı!', 'Hesabınız başka bir cihazdan açılmış.', () => {
                                        window.location.href = 'login.html';
                                    });
                                }
                            }
                        }
                    } catch (error) {
                        console.error('Periodic session validation error:', error);
                    }
                }, 30000); // Check every 30 seconds
                
                // Set user info
                document.getElementById('username').textContent = user.username;
                
                // Show admin panel if user is admin
                if (user.role === 'admin') {
                    const adminTab = document.querySelector('.tab-button[data-tab="admin"]');
                    if (adminTab) {
                        adminTab.style.display = 'flex';
                    }
                }
                
                // Setup logout functionality
                document.getElementById('logout-btn').addEventListener('click', async () => {
                    try {
                        await ipcRenderer.invoke('auth-logout');
                        
                        // Clear all session data on logout
                        localStorage.removeItem('currentUser');
                        localStorage.removeItem('autoLogin');
                        localStorage.removeItem('savedCredentials');
                        
                        window.location.href = 'login.html';
                    } catch (error) {
                        console.error('Logout error:', error);
                        
                        // Clear all data even on error
                        localStorage.removeItem('currentUser');
                        localStorage.removeItem('autoLogin');
                        localStorage.removeItem('savedCredentials');
                        
                        window.location.href = 'login.html';
                    }
                });
                
            } catch (error) {
                console.error('Auth check error:', error);
                localStorage.removeItem('currentUser');
                window.location.href = 'login.html';
            }
        });

        // Security Notification Function
        function showSecurityNotification(title, message, callback) {
            const overlay = document.getElementById('securityNotificationOverlay');
            const titleElement = document.getElementById('securityNotificationTitle');
            const messageElement = document.getElementById('securityNotificationMessage');
            const btnElement = document.getElementById('securityNotificationBtn');
            
            titleElement.textContent = title;
            messageElement.textContent = message;
            
            overlay.classList.add('active');
            
            // Add click handler
            const handleClick = () => {
                overlay.classList.remove('active');
                btnElement.removeEventListener('click', handleClick);
                if (callback) {
                    setTimeout(callback, 300); // Small delay for animation
                }
            };
            
            btnElement.addEventListener('click', handleClick);
            
            // Auto-close after 10 seconds if no interaction
            setTimeout(() => {
                if (overlay.classList.contains('active')) {
                    handleClick();
                }
            }, 10000);
        }
    </script>
    <script src="renderer.js"></script>
</body>
</html>